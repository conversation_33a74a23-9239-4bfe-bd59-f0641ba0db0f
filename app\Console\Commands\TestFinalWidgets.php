<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Filament\Widgets\SalesForCollectionWidget;
use App\Filament\Widgets\CurrentMonthReceiptsWidget;
use App\Filament\Widgets\OverdueSalesWidget;
use App\Filament\Widgets\NextMonthForecastWidget;
use App\Filament\Widgets\CollectionPerformanceWidget;

class TestFinalWidgets extends Command
{
    protected $signature = 'test:final-widgets';
    protected $description = 'Test final dashboard widgets';

    public function handle()
    {
        $this->info('🎯 Testando Widgets Finais do Dashboard...');

        $widgets = [
            '1. 💰 Vendas para Cobrança' => SalesForCollectionWidget::class,
            '2. 💵 Recebimentos do Mês' => CurrentMonthReceiptsWidget::class,
            '3. 🚨 Vendas em Atraso' => OverdueSalesWidget::class,
            '4. 🔮 Previsão Próximo Mês' => NextMonthForecastWidget::class,
            '5. 🎯 Performance de Cobrança' => CollectionPerformanceWidget::class,
        ];

        foreach ($widgets as $name => $class) {
            try {
                $this->info($name);
                new $class();
                $this->info('   ✅ Funcionando perfeitamente');
            } catch (\Exception $e) {
                $this->error('   ❌ Erro: ' . $e->getMessage());
                return 1;
            }
        }

        $this->info('');
        $this->info('🎉 Dashboard limpo! Apenas os 5 widgets essenciais estão ativos.');
        $this->info('📊 Acesse /admin para ver o dashboard otimizado para seu negócio');

        return 0;
    }
}
