<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class NextMonthForecastWidget extends BaseWidget
{
    protected static ?int $sort = 40;

    protected function getStats(): array
    {
        $now = Carbon::now();

        // Vendas do mês atual que serão cobradas no próximo mês
        $currentMonth = $now->copy()->startOfMonth();
        $endOfMonth = $now->copy()->endOfMonth();

        $currentMonthSales = Sale::whereBetween('sale_date', [$currentMonth, $endOfMonth])->get();

        $totalForecast = $currentMonthSales->sum('total');
        $forecastQuantity = $currentMonthSales->sum('quantity');
        $forecastClients = $currentMonthSales->pluck('person_id')->unique()->count();

        // Calcular taxa de conversão histórica para projeção
        $previousMonth = $now->copy()->startOfMonth()->subMonth();
        $endOfPreviousMonth = $previousMonth->copy()->endOfMonth();

        $previousSales = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])->get();
        $previousPaid = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])
            ->where('paid', true)
            ->get();

        $historicalConversionRate = $previousSales->sum('total') > 0
            ? ($previousPaid->sum('total') / $previousSales->sum('total')) * 100
            : 80; // Default 80% se não houver histórico

        $projectedReceipt = $totalForecast * ($historicalConversionRate / 100);

        // Comparação com vendas do mês passado
        $previousTotalSales = $previousSales->sum('total');
        $growthRate = $previousTotalSales > 0
            ? (($totalForecast - $previousTotalSales) / $previousTotalSales) * 100
            : 0;

        // Média de vendas dos últimos 3 meses para comparação
        $threeMonthsAgo = $now->copy()->startOfMonth()->subMonths(3);
        $lastThreeMonthsSales = Sale::where('sale_date', '>=', $threeMonthsAgo)
            ->where('sale_date', '<', $currentMonth)
            ->sum('total');
        $averageLastThreeMonths = $lastThreeMonthsSales / 3;

        $nextMonthName = $now->copy()->addMonth()->locale('pt_BR')->isoFormat('MMMM YYYY');

        return [
            Stat::make('🔮 Previsão ' . $nextMonthName, 'R$ ' . number_format($totalForecast, 2, ',', '.'))
                ->description('Vendas atuais para cobrança futura')
                ->descriptionIcon('heroicon-m-calendar-days')
                ->color('info')
                ->chart([15, 18, 22, 25, 28, 32, 35]),

            Stat::make('💡 Recebimento Projetado', 'R$ ' . number_format($projectedReceipt, 2, ',', '.'))
                ->description('Baseado em ' . number_format($historicalConversionRate, 1) . '% de conversão histórica')
                ->descriptionIcon('heroicon-m-light-bulb')
                ->color($historicalConversionRate >= 70 ? 'success' : 'warning')
                ->chart([12, 15, 18, 21, 24, 27, 30]),

            Stat::make('📈 Crescimento Projetado', number_format($growthRate, 1) . '%')
                ->description($growthRate >= 0
                    ? 'Crescimento vs mês anterior'
                    : 'Queda vs mês anterior'
                )
                ->descriptionIcon($growthRate >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($growthRate >= 0 ? 'success' : 'danger')
                ->chart([8, 10, 12, 14, 16, 18, 20]),
        ];
    }
}
