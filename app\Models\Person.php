<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Person extends Model
{
    protected $fillable = [
        'name',
        'phone',
        'department_id',
    ];

    public function getPhoneWithDDIAttribute()
    {
        $phone = preg_replace('/[^0-9]/', '', $this->phone);
        if (str_starts_with($phone, '55')) {
            return $phone;
        }

        return "55{$phone}";
    }

    public function setPhoneAttribute($value)
    {
        $this->attributes['phone'] = preg_replace('/[^0-9]/', '', $value);
    }

    public function department()
    {
        return $this->belongsTo(Department::class);
    }

    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    public function chargeRequests()
    {
        return $this->hasMany(ChargeRequest::class);
    }
}
