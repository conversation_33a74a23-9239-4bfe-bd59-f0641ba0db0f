<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Sale extends Model
{
    protected $fillable = [
        'person_id',
        'product_id',
        'quantity',
        'unit_price',
        'total',
        'paid',
        'sale_date',
        'payment_date',
        'observations',
    ];

    protected $casts = [
        'sale_date' => 'date',
        'payment_date' => 'date',
        'paid' => 'boolean',
        'total' => 'decimal:2',
    ];

    public function person()
    {
        return $this->belongsTo(Person::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }
}
