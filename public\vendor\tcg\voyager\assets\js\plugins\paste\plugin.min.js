!function(e,t){"use strict";function n(e,t){for(var n,r=[],o=0;o<e.length;++o){if(n=a[e[o]]||i(e[o]),!n)throw"module definition dependecy not found: "+e[o];r.push(n)}t.apply(null,r)}function r(e,r,i){if("string"!=typeof e)throw"invalid module definition, module id must be defined and be a string";if(r===t)throw"invalid module definition, dependencies must be specified";if(i===t)throw"invalid module definition, definition function must be specified";n(r,function(){a[e]=i.apply(null,arguments)})}function i(t){for(var n=e,r=t.split(/[.\/]/),i=0;i<r.length;++i){if(!n[r[i]])return;n=n[r[i]]}return n}function o(n){var r,i,o,s,l;for(r=0;r<n.length;r++){i=e,o=n[r],s=o.split(/[.\/]/);for(var c=0;c<s.length-1;++c)i[s[c]]===t&&(i[s[c]]={}),i=i[s[c]];i[s[s.length-1]]=a[o]}if(e.AMDLC_TESTS){l=e.privateModules||{};for(o in a)l[o]=a[o];for(r=0;r<n.length;r++)delete l[n[r]];e.privateModules=l}}var a={};r("tinymce/pasteplugin/Utils",["tinymce/util/Tools","tinymce/html/DomParser","tinymce/html/Schema"],function(e,t,n){function r(t,n){return e.each(n,function(e){t=e.constructor==RegExp?t.replace(e,""):t.replace(e[0],e[1])}),t}function i(i){function o(e){var t=e.name,n=e;if("br"===t)return void(l+="\n");if(c[t]&&(l+=" "),u[t])return void(l+=" ");if(3==e.type&&(l+=e.value),!e.shortEnded&&(e=e.firstChild))do o(e);while(e=e.next);d[t]&&n.next&&(l+="\n","p"==t&&(l+="\n"))}var a=new n,s=new t({},a),l="",c=a.getShortEndedElements(),u=e.makeMap("script noscript style textarea video audio iframe object"," "),d=a.getBlockElements();return i=r(i,[/<!\[[^\]]+\]>/g]),o(s.parse(i)),l}function o(e){function t(e,t,n){return t||n?"\xa0":" "}return e=r(e,[/^[\s\S]*<body[^>]*>\s*|\s*<\/body[^>]*>[\s\S]*$/g,/<!--StartFragment-->|<!--EndFragment-->/g,[/( ?)<span class="Apple-converted-space">\u00a0<\/span>( ?)/g,t],/<br class="Apple-interchange-newline">/g,/<br>$/i])}function a(e){var t=0;return function(){return e+t++}}return{filter:r,innerText:i,trimHtml:o,createIdGenerator:a}}),r("tinymce/pasteplugin/SmartPaste",["tinymce/util/Tools"],function(e){var t=function(e){return/^https?:\/\/[\w\?\-\/+=.&%@~#]+$/i.test(e)},n=function(e){return t(e)&&/.(gif|jpe?g|png)$/.test(e)},r=function(e,t,n){return e.undoManager.extra(function(){n(e,t)},function(){e.insertContent('<img src="'+t+'">')}),!0},i=function(e,t,n){return e.undoManager.extra(function(){n(e,t)},function(){e.execCommand("mceInsertLink",!1,t)}),!0},o=function(e,n,r){return!(e.selection.isCollapsed()!==!1||!t(n))&&i(e,n,r)},a=function(e,t,i){return!!n(t)&&r(e,t,i)},s=function(e,t){return e.insertContent(t,{merge:e.settings.paste_merge_formats!==!1,paste:!0}),!0},l=function(t,n){e.each([o,a,s],function(e){return e(t,n,s)!==!0})},c=function(e,t){e.settings.smart_paste===!1?s(e,t):l(e,t)};return{isImageUrl:n,isAbsoluteUrl:t,insertContent:c}}),r("tinymce/pasteplugin/Clipboard",["tinymce/Env","tinymce/dom/RangeUtils","tinymce/util/VK","tinymce/pasteplugin/Utils","tinymce/pasteplugin/SmartPaste","tinymce/util/Delay"],function(e,t,n,r,i,o){return function(a){function s(e){var t,n=a.dom;if(t=a.fire("BeforePastePreProcess",{content:e}),t=a.fire("PastePreProcess",t),e=t.content,!t.isDefaultPrevented()){if(a.hasEventListeners("PastePostProcess")&&!t.isDefaultPrevented()){var r=n.add(a.getBody(),"div",{style:"display:none"},e);t=a.fire("PastePostProcess",{node:r}),n.remove(r),e=t.node.innerHTML}t.isDefaultPrevented()||i.insertContent(a,e)}}function l(e){e=a.dom.encode(e).replace(/\r\n/g,"\n");var t,n=a.dom.getParent(a.selection.getStart(),a.dom.isBlock),i=a.settings.forced_root_block;i&&(t=a.dom.createHTML(i,a.settings.forced_root_block_attrs),t=t.substr(0,t.length-3)+">"),n&&/^(PRE|DIV)$/.test(n.nodeName)||!i?e=r.filter(e,[[/\n/g,"<br>"]]):(e=r.filter(e,[[/\n\n/g,"</p>"+t],[/^(.*<\/p>)(<p>)$/,t+"$1"],[/\n/g,"<br />"]]),e.indexOf("<p>")!=-1&&(e=t+e)),s(e)}function c(){function t(e){var t,n,i,o=e.startContainer;if(t=e.getClientRects(),t.length)return t[0];if(e.collapsed&&1==o.nodeType){for(i=o.childNodes[S.startOffset];i&&3==i.nodeType&&!i.data.length;)i=i.nextSibling;if(i)return"BR"==i.tagName&&(n=r.doc.createTextNode("\ufeff"),i.parentNode.insertBefore(n,i),e=r.createRng(),e.setStartBefore(n),e.setEndAfter(n),t=e.getClientRects(),r.remove(n)),t.length?t[0]:void 0}}var n,r=a.dom,i=a.getBody(),o=a.dom.getViewPort(a.getWin()),s=o.y,l=20;if(S=a.selection.getRng(),a.inline&&(n=a.selection.getScrollContainer(),n&&n.scrollTop>0&&(s=n.scrollTop)),S.getClientRects){var c=t(S);if(c)l=s+(c.top-r.getPos(i).y);else{l=s;var u=S.startContainer;u&&(3==u.nodeType&&u.parentNode!=i&&(u=u.parentNode),1==u.nodeType&&(l=r.getPos(u,n||i).y))}}E=r.add(a.getBody(),"div",{id:"mcepastebin",contentEditable:!0,"data-mce-bogus":"all",style:"position: absolute; top: "+l+"px;width: 10px; height: 10px; overflow: hidden; opacity: 0"},A),(e.ie||e.gecko)&&r.setStyle(E,"left","rtl"==r.getStyle(i,"direction",!0)?65535:-65535),r.bind(E,"beforedeactivate focusin focusout",function(e){e.stopPropagation()}),E.focus(),a.selection.select(E,!0)}function u(){if(E){for(var e;e=a.dom.get("mcepastebin");)a.dom.remove(e),a.dom.unbind(e);S&&a.selection.setRng(S)}E=S=null}function d(){var e,t,n,r,i="";for(e=a.dom.select("div[id=mcepastebin]"),t=0;t<e.length;t++)n=e[t],n.firstChild&&"mcepastebin"==n.firstChild.id&&(n=n.firstChild),r=n.innerHTML,i!=A&&(i+=r);return i}function f(e){var t={};if(e){if(e.getData){var n=e.getData("Text");n&&n.length>0&&n.indexOf(B)==-1&&(t["text/plain"]=n)}if(e.types)for(var r=0;r<e.types.length;r++){var i=e.types[r];t[i]=e.getData(i)}}return t}function p(e){return f(e.clipboardData||a.getDoc().dataTransfer)}function m(e){return x(e,"text/html")||x(e,"text/plain")}function h(e){var t;return t=e.indexOf(","),t!==-1?e.substr(t+1):null}function g(e,t){return!e.images_dataimg_filter||e.images_dataimg_filter(t)}function v(e,t,n){e&&(a.selection.setRng(e),e=null);var r=t.result,i=h(r),o=new Image;if(o.src=r,g(a.settings,o)){var l,c,u=a.editorUpload.blobCache;c=u.findFirst(function(e){return e.base64()===i}),c?l=c:(l=u.create(P(),n,i),u.add(l)),s('<img src="'+l.blobUri()+'">')}else s('<img src="'+r+'">')}function b(e,t){function n(n){var r,i,o,a=!1;if(n)for(r=0;r<n.length;r++)if(i=n[r],/^image\/(jpeg|png|gif|bmp)$/.test(i.type)){var s=i.getAsFile?i.getAsFile():i;o=new FileReader,o.onload=v.bind(null,t,o,s),o.readAsDataURL(s),e.preventDefault(),a=!0}return a}var r=e.clipboardData||e.dataTransfer;if(a.settings.paste_data_images&&r)return n(r.items)||n(r.files)}function y(e){var t=e.clipboardData;return navigator.userAgent.indexOf("Android")!=-1&&t&&t.items&&0===t.items.length}function C(e){return t.getCaretRangeFromPoint(e.clientX,e.clientY,a.getDoc())}function x(e,t){return t in e&&e[t].length>0}function w(e){return n.metaKeyPressed(e)&&86==e.keyCode||e.shiftKey&&45==e.keyCode}function N(){function t(e,t,n){var i;return x(e,"text/html")?i=e["text/html"]:(i=d(),i==A&&(n=!0)),i=r.trimHtml(i),E&&E.firstChild&&"mcepastebin"===E.firstChild.id&&(n=!0),u(),i.length||(n=!0),n&&(i=x(e,"text/plain")&&i.indexOf("</p>")==-1?e["text/plain"]:r.innerText(i)),i==A?void(t||a.windowManager.alert("Please use Ctrl+V/Cmd+V keyboard shortcuts to paste contents.")):void(n?l(i):s(i))}function n(e){var t=e["text/plain"];return!!t&&0===t.indexOf("file://")}a.on("keydown",function(t){function n(e){w(e)&&!e.isDefaultPrevented()&&u()}if(w(t)&&!t.isDefaultPrevented()){if(_=t.shiftKey&&86==t.keyCode,_&&e.webkit&&navigator.userAgent.indexOf("Version/")!=-1)return;if(t.stopImmediatePropagation(),T=(new Date).getTime(),e.ie&&_)return t.preventDefault(),void a.fire("paste",{ieFake:!0});u(),c(),a.once("keyup",n),a.once("paste",function(){a.off("keyup",n)})}});var i=function(){return S||a.selection.getRng()};a.on("paste",function(n){var r=(new Date).getTime(),s=p(n),l=(new Date).getTime()-r,f=(new Date).getTime()-T-l<1e3,h="text"==k.pasteFormat||_;return _=!1,n.isDefaultPrevented()||y(n)?void u():!m(s)&&b(n,i())?void u():(f||n.preventDefault(),!e.ie||f&&!n.ieFake||(c(),a.dom.bind(E,"paste",function(e){e.stopPropagation()}),a.getDoc().execCommand("Paste",!1,null),s["text/html"]=d()),void(x(s,"text/html")?(n.preventDefault(),t(s,f,h)):o.setEditorTimeout(a,function(){t(s,f,h)},0)))}),a.on("dragstart dragend",function(e){R="dragstart"==e.type}),a.on("drop",function(e){var t,i;if(i=C(e),!e.isDefaultPrevented()&&!R&&(t=f(e.dataTransfer),(m(t)&&!n(t)||!b(e,i))&&i&&a.settings.paste_filter_drop!==!1)){var c=t["mce-internal"]||t["text/html"]||t["text/plain"];c&&(e.preventDefault(),o.setEditorTimeout(a,function(){a.undoManager.transact(function(){t["mce-internal"]&&a.execCommand("Delete"),a.selection.setRng(i),c=r.trimHtml(c),t["text/html"]?s(c):l(c)})}))}}),a.on("dragover dragend",function(e){a.settings.paste_data_images&&e.preventDefault()})}var E,S,_,k=this,T=0,R=!1,A="%MCEPASTEBIN%",B="data:text/mce-internal,",P=r.createIdGenerator("mceclip");k.pasteHtml=s,k.pasteText=l,k.pasteImageData=b,a.on("preInit",function(){N(),a.parser.addNodeFilter("img",function(t,n,r){function i(e){return e.data&&e.data.paste===!0}function o(t){t.attr("data-mce-object")||u===e.transparentSrc||t.remove()}function s(e){return 0===e.indexOf("webkit-fake-url")}function l(e){return 0===e.indexOf("data:")}if(!a.settings.paste_data_images&&i(r))for(var c=t.length;c--;){var u=t[c].attributes.map.src;u&&(s(u)?o(t[c]):!a.settings.allow_html_data_urls&&l(u)&&o(t[c]))}})})}}),r("tinymce/pasteplugin/WordFilter",["tinymce/util/Tools","tinymce/html/DomParser","tinymce/html/Schema","tinymce/html/Serializer","tinymce/html/Node","tinymce/pasteplugin/Utils"],function(e,t,n,r,i,o){function a(e){return/<font face="Times New Roman"|class="?Mso|style="[^"]*\bmso-|style='[^'']*\bmso-|w:WordDocument/i.test(e)||/class="OutlineElement/.test(e)||/id="?docs\-internal\-guid\-/.test(e)}function s(t){var n,r;return r=[/^[IVXLMCD]{1,2}\.[ \u00a0]/,/^[ivxlmcd]{1,2}\.[ \u00a0]/,/^[a-z]{1,2}[\.\)][ \u00a0]/,/^[A-Z]{1,2}[\.\)][ \u00a0]/,/^[0-9]+\.[ \u00a0]/,/^[\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d]+\.[ \u00a0]/,/^[\u58f1\u5f10\u53c2\u56db\u4f0d\u516d\u4e03\u516b\u4e5d\u62fe]+\.[ \u00a0]/],t=t.replace(/^[\u00a0 ]+/,""),e.each(r,function(e){if(e.test(t))return n=!0,!1}),n}function l(e){return/^[\s\u00a0]*[\u2022\u00b7\u00a7\u25CF]\s*/.test(e)}function c(c){var u=c.settings;c.on("BeforePastePreProcess",function(d){function f(e){function t(e){var n="";if(3===e.type)return e.value;if(e=e.firstChild)do n+=t(e);while(e=e.next);return n}function n(e,t){if(3===e.type&&t.test(e.value))return e.value=e.value.replace(t,""),!1;if(e=e.firstChild)do if(!n(e,t))return!1;while(e=e.next);return!0}function r(e){if(e._listIgnore)return void e.remove();if(e=e.firstChild)do r(e);while(e=e.next)}function o(e,t,o){var s=e._listLevel||u;s!=u&&(s<u?a&&(a=a.parent.parent):(c=a,a=null)),a&&a.name==t?a.append(e):(c=c||a,a=new i(t,1),o>1&&a.attr("start",""+o),e.wrap(a)),e.name="li",s>u&&c&&c.lastChild.append(a),u=s,r(e),n(e,/^\u00a0+/),n(e,/^\s*([\u2022\u00b7\u00a7\u25CF]|\w+\.)/),n(e,/^\u00a0+/)}for(var a,c,u=1,d=[],f=e.firstChild;"undefined"!=typeof f&&null!==f;)if(d.push(f),f=f.walk(),null!==f)for(;"undefined"!=typeof f&&f.parent!==e;)f=f.walk();for(var p=0;p<d.length;p++)if(e=d[p],"p"==e.name&&e.firstChild){var m=t(e);if(l(m)){o(e,"ul");continue}if(s(m)){var h=/([0-9]+)\./.exec(m),g=1;h&&(g=parseInt(h[1],10)),o(e,"ol",g);continue}if(e._listLevel){o(e,"ul",1);continue}a=null}else c=a,a=null}function p(t,n){var r,o={},a=c.dom.parseStyle(n);return e.each(a,function(e,i){switch(i){case"mso-list":r=/\w+ \w+([0-9]+)/i.exec(n),r&&(t._listLevel=parseInt(r[1],10)),/Ignore/i.test(e)&&t.firstChild&&(t._listIgnore=!0,t.firstChild._listIgnore=!0);break;case"horiz-align":i="text-align";break;case"vert-align":i="vertical-align";break;case"font-color":case"mso-foreground":i="color";break;case"mso-background":case"mso-highlight":i="background";break;case"font-weight":case"font-style":return void("normal"!=e&&(o[i]=e));case"mso-element":if(/^(comment|comment-list)$/i.test(e))return void t.remove()}return 0===i.indexOf("mso-comment")?void t.remove():void(0!==i.indexOf("mso-")&&("all"==m||h&&h[i])&&(o[i]=e))}),/(bold)/i.test(o["font-weight"])&&(delete o["font-weight"],t.wrap(new i("b",1))),/(italic)/i.test(o["font-style"])&&(delete o["font-style"],t.wrap(new i("i",1))),o=c.dom.serializeStyle(o,t.name),o?o:null}var m,h,g=d.content;if(g=g.replace(/<b[^>]+id="?docs-internal-[^>]*>/gi,""),g=g.replace(/<br class="?Apple-interchange-newline"?>/gi,""),m=u.paste_retain_style_properties,m&&(h=e.makeMap(m.split(/[, ]/))),u.paste_enable_default_filters!==!1&&a(d.content)){d.wordContent=!0,g=o.filter(g,[/<!--[\s\S]+?-->/gi,/<(!|script[^>]*>.*?<\/script(?=[>\s])|\/?(\?xml(:\w+)?|img|meta|link|style|\w:\w+)(?=[\s\/>]))[^>]*>/gi,[/<(\/?)s>/gi,"<$1strike>"],[/&nbsp;/gi,"\xa0"],[/<span\s+style\s*=\s*"\s*mso-spacerun\s*:\s*yes\s*;?\s*"\s*>([\s\u00a0]*)<\/span>/gi,function(e,t){return t.length>0?t.replace(/./," ").slice(Math.floor(t.length/2)).split("").join("\xa0"):""}]]);var v=u.paste_word_valid_elements;v||(v="-strong/b,-em/i,-u,-span,-p,-ol,-ul,-li,-h1,-h2,-h3,-h4,-h5,-h6,-p/div,-a[href|name],sub,sup,strike,br,del,table[width],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody");var b=new n({valid_elements:v,valid_children:"-li[p]"});e.each(b.elements,function(e){e.attributes["class"]||(e.attributes["class"]={},e.attributesOrder.push("class")),e.attributes.style||(e.attributes.style={},e.attributesOrder.push("style"))});var y=new t({},b);y.addAttributeFilter("style",function(e){for(var t,n=e.length;n--;)t=e[n],t.attr("style",p(t,t.attr("style"))),"span"==t.name&&t.parent&&!t.attributes.length&&t.unwrap()}),y.addAttributeFilter("class",function(e){for(var t,n,r=e.length;r--;)t=e[r],n=t.attr("class"),/^(MsoCommentReference|MsoCommentText|msoDel)$/i.test(n)&&t.remove(),t.attr("class",null)}),y.addNodeFilter("del",function(e){for(var t=e.length;t--;)e[t].remove()}),y.addNodeFilter("a",function(e){for(var t,n,r,i=e.length;i--;)if(t=e[i],n=t.attr("href"),r=t.attr("name"),n&&n.indexOf("#_msocom_")!=-1)t.remove();else if(n&&0===n.indexOf("file://")&&(n=n.split("#")[1],n&&(n="#"+n)),n||r){if(r&&!/^_?(?:toc|edn|ftn)/i.test(r)){t.unwrap();continue}t.attr({href:n,name:r})}else t.unwrap()});var C=y.parse(g);u.paste_convert_word_fake_lists!==!1&&f(C),d.content=new r({validate:u.validate},b).serialize(C)}})}return c.isWordContent=a,c}),r("tinymce/pasteplugin/Quirks",["tinymce/Env","tinymce/util/Tools","tinymce/pasteplugin/WordFilter","tinymce/pasteplugin/Utils"],function(e,t,n,r){return function(i){function o(e){i.on("BeforePastePreProcess",function(t){t.content=e(t.content)})}function a(e){i.on("PastePostProcess",function(t){e(t.node)})}function s(e){if(!n.isWordContent(e))return e;var o=[];t.each(i.schema.getBlockElements(),function(e,t){o.push(t)});var a=new RegExp("(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*(<\\/?("+o.join("|")+")[^>]*>)(?:<br>&nbsp;[\\s\\r\\n]+|<br>)*","g");return e=r.filter(e,[[a,"$1"]]),e=r.filter(e,[[/<br><br>/g,"<BR><BR>"],[/<br>/g," "],[/<BR><BR>/g,"<br>"]])}function l(e){if(n.isWordContent(e))return e;var t=i.settings.paste_webkit_styles;if(i.settings.paste_remove_styles_if_webkit===!1||"all"==t)return e;if(t&&(t=t.split(/[, ]/)),t){var r=i.dom,o=i.selection.getNode();e=e.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,function(e,n,i,a){var s=r.parseStyle(i,"span"),l={};if("none"===t)return n+a;for(var c=0;c<t.length;c++){var u=s[t[c]],d=r.getStyle(o,t[c],!0);/color/.test(t[c])&&(u=r.toHex(u),d=r.toHex(d)),d!=u&&(l[t[c]]=u)}return l=r.serializeStyle(l,"span"),l?n+' style="'+l+'"'+a:n+a})}else e=e.replace(/(<[^>]+) style="([^"]*)"([^>]*>)/gi,"$1$3");return e=e.replace(/(<[^>]+) data-mce-style="([^"]+)"([^>]*>)/gi,function(e,t,n,r){return t+' style="'+n+'"'+r})}function c(e){i.$("a",e).find("font,u").each(function(e,t){i.dom.remove(t,!0)})}e.webkit&&o(l),e.ie&&(o(s),a(c))}}),r("tinymce/pasteplugin/Plugin",["tinymce/PluginManager","tinymce/pasteplugin/Clipboard","tinymce/pasteplugin/WordFilter","tinymce/pasteplugin/Quirks"],function(e,t,n,r){var i;e.add("paste",function(o){function a(){return i||o.settings.paste_plaintext_inform===!1}function s(){if("text"==c.pasteFormat)c.pasteFormat="html",o.fire("PastePlainTextToggle",{state:!1});else if(c.pasteFormat="text",o.fire("PastePlainTextToggle",{state:!0}),!a()){var e=o.translate("Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.");o.notificationManager.open({text:e,type:"info"}),i=!0}o.focus()}function l(){var e=this;e.active("text"===c.pasteFormat),o.on("PastePlainTextToggle",function(t){e.active(t.state)})}var c,u=this,d=o.settings;return/(^|[ ,])powerpaste([, ]|$)/.test(d.plugins)&&e.get("powerpaste")?void("undefined"!=typeof console&&console.log&&console.log("PowerPaste is incompatible with Paste plugin! Remove 'paste' from the 'plugins' option.")):(u.clipboard=c=new t(o),u.quirks=new r(o),u.wordFilter=new n(o),o.settings.paste_as_text&&(u.clipboard.pasteFormat="text"),d.paste_preprocess&&o.on("PastePreProcess",function(e){d.paste_preprocess.call(u,u,e)}),d.paste_postprocess&&o.on("PastePostProcess",function(e){d.paste_postprocess.call(u,u,e)}),o.addCommand("mceInsertClipboardContent",function(e,t){t.content&&u.clipboard.pasteHtml(t.content),t.text&&u.clipboard.pasteText(t.text)}),o.settings.paste_block_drop&&o.on("dragend dragover draggesture dragdrop drop drag",function(e){e.preventDefault(),e.stopPropagation()}),o.settings.paste_data_images||o.on("drop",function(e){var t=e.dataTransfer;t&&t.files&&t.files.length>0&&e.preventDefault()}),o.addCommand("mceTogglePlainTextPaste",s),o.addButton("pastetext",{icon:"pastetext",tooltip:"Paste as text",onclick:s,onPostRender:l}),void o.addMenuItem("pastetext",{text:"Paste as text",selectable:!0,active:c.pasteFormat,onclick:s,onPostRender:l}))})}),o(["tinymce/pasteplugin/Utils"])}(window);
