<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class CurrentMonthReceiptsWidget extends BaseWidget
{
    protected static ?int $sort = 20;

    protected function getStats(): array
    {
        $now = Carbon::now();
        
        // Recebimentos deste mês (vendas do mês passado que foram pagas este mês)
        $currentMonth = $now->copy()->startOfMonth();
        $endOfMonth = $now->copy()->endOfMonth();
        
        // Vendas do mês passado
        $previousMonth = $now->copy()->startOfMonth()->subMonth();
        $endOfPreviousMonth = $previousMonth->copy()->endOfMonth();
        
        $previousMonthSales = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])->get();
        $totalExpected = $previousMonthSales->sum('total');
        
        // Vendas do mês passado que foram pagas este mês
        $paidThisMonth = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])
            ->where('paid', true)
            ->whereBetween('payment_date', [$currentMonth, $endOfMonth])
            ->get();
        
        $totalReceived = $paidThisMonth->sum('total');
        $clientsPaid = $paidThisMonth->pluck('person_id')->unique()->count();
        
        // Taxa de conversão
        $conversionRate = $totalExpected > 0 ? ($totalReceived / $totalExpected) * 100 : 0;
        
        // Vendas do mês passado que já estavam pagas (pagas no mesmo mês da venda)
        $alreadyPaid = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])
            ->where('paid', true)
            ->whereBetween('payment_date', [$previousMonth, $endOfPreviousMonth])
            ->sum('total');
        
        $totalReceivedIncludingPrevious = $totalReceived + $alreadyPaid;
        $overallConversionRate = $totalExpected > 0 ? ($totalReceivedIncludingPrevious / $totalExpected) * 100 : 0;

        return [
            Stat::make('💵 Recebido Este Mês', 'R$ ' . number_format($totalReceived, 2, ',', '.'))
                ->description('De vendas do mês passado cobradas agora')
                ->descriptionIcon('heroicon-m-banknotes')
                ->color('success')
                ->chart([8, 12, 16, 20, 24, 28, 32]),

            Stat::make('📊 Taxa de Cobrança', number_format($conversionRate, 1) . '%')
                ->description('R$ ' . number_format($totalReceived, 2, ',', '.') . ' de R$ ' . number_format($totalExpected, 2, ',', '.'))
                ->descriptionIcon($conversionRate >= 70 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($conversionRate >= 70 ? 'success' : ($conversionRate >= 50 ? 'warning' : 'danger'))
                ->chart([40, 50, 60, 70, 75, 80, 85]),

            Stat::make('✅ Clientes que Pagaram', $clientsPaid . ' clientes')
                ->description('Taxa geral: ' . number_format($overallConversionRate, 1) . '%')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color($overallConversionRate >= 80 ? 'success' : 'info')
                ->chart([5, 8, 12, 15, 18, 22, 25]),
        ];
    }
}
