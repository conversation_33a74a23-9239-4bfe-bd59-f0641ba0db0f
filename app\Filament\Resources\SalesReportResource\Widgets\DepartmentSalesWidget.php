<?php

namespace App\Filament\Resources\SalesReportResource\Widgets;

use App\Models\Sale;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class DepartmentSalesWidget extends ChartWidget
{
    protected static ?string $heading = 'Vendas por Departamento';
    protected static ?int $sort = 5;

    protected function getData(): array
    {
        // Pegar vendas agrupadas por departamento através da pessoa
        $departmentSales = Sale::select(
                'departments.name as department_name',
                DB::raw('SUM(sales.total) as total_sales')
            )
            ->join('people', 'sales.person_id', '=', 'people.id')
            ->join('departments', 'people.department_id', '=', 'departments.id')
            ->groupBy('departments.id', 'departments.name')
            ->orderBy('total_sales', 'desc')
            ->get();

        $labels = [];
        $data = [];
        $colors = [
            'rgba(255, 99, 132, 0.8)',
            'rgba(54, 162, 235, 0.8)',
            'rgba(255, 205, 86, 0.8)',
            'rgba(75, 192, 192, 0.8)',
            'rgba(153, 102, 255, 0.8)',
            'rgba(255, 159, 64, 0.8)',
            'rgba(199, 199, 199, 0.8)',
            'rgba(83, 102, 255, 0.8)',
        ];

        foreach ($departmentSales as $sale) {
            $labels[] = $sale->department_name ?? 'Sem Departamento';
            $data[] = (float) $sale->total_sales;
        }

        // Se não houver dados, mostrar mensagem
        if (empty($data)) {
            $labels = ['Nenhum dado'];
            $data = [0];
            $colors = ['rgba(200, 200, 200, 0.8)'];
        }

        return [
            'datasets' => [
                [
                    'label' => 'Vendas (R$)',
                    'data' => $data,
                    'backgroundColor' => array_slice($colors, 0, count($data)),
                    'borderWidth' => 1,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => false,
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) { 
                            return "R$ " + context.parsed.y.toLocaleString("pt-BR", {minimumFractionDigits: 2}); 
                        }',
                    ],
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'callback' => 'function(value) { return "R$ " + value.toLocaleString("pt-BR"); }',
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
