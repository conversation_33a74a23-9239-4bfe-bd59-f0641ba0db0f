<?php

return [
    'date' => [
        'last_week' => 'На прошлой неделе',
        'last_year' => 'В прошлом году',
        'this_week' => 'На этой неделе',
        'this_year' => 'В этом году',
    ],

    'generic' => [
        'action'                 => 'Действие',
        'actions'                => 'Доступные действия',
        'add'                    => 'Добавить',
        'add_folder'             => 'Создать папку',
        'add_new'                => 'Добавить',
        'all_done'               => 'Готово',
        'are_you_sure'           => 'Вы уверены',
        'are_you_sure_delete'    => 'Вы точно хотите удалить',
        'auto_increment'         => 'Auto Increment',
        'browse'                 => 'Просмотр',
        'builder'                => 'Конструктор',
    'bulk_delete'                => 'Удалить всё',
        'bulk_delete_confirm'    => 'Да, удалить это',
        'bulk_delete_nothing'    => 'Вы ничего не выбрали для удаления!',
        'cancel'                 => 'Отмена',
        'choose_type'            => 'Выберите тип поля',
        'click_here'             => 'Кликните тут',
        'close'                  => 'Закрыть',
    'compass'                    => 'Компасс',
        'created_at'             => 'Дата создания',
        'custom'                 => 'Пользовательская категория',
        'dashboard'              => 'Панель управления',
        'database'               => 'База данных',
        'default'                => 'По умолчанию',
        'delete'                 => 'Удалить',
        'delete_confirm'         => 'Да, удалить!',
        'delete_question'        => 'Вы действительно хотите удалить это?',
        'delete_this_confirm'    => 'Да, удалить это',
        'deselect_all'           => 'Отменить выделение',
        'download'               => 'Загрузка',
        'edit'                   => 'Редактирование',
        'email'                  => 'E-mail',
        'error_deleting'         => 'Во время удаления возникла ошибка',
        'exception'              => 'Исключение',
        'featured'               => 'Рекомендуемый',
        'field_does_not_exist'   => 'Поля не существует',
        'how_to_use'             => 'Как использовать',
        'index'                  => 'Индекс',
        'internal_error'         => 'Внутреняя ошибка',
        'items'                  => 'элемент(ы)',
        'keep_sidebar_open'      => 'Раскрывать панель',
        'key'                    => 'Ключ',
        'last_modified'          => 'Последнее изменение',
        'length'                 => 'Длина',
        'login'                  => 'Логин',
        'media'                  => 'Медиа',
        'menu_builder'           => 'Конструктор меню',
        'move'                   => 'Переместить',
        'name'                   => 'Имя',
        'new'                    => 'Новинка',
        'no'                     => 'Нет',
        'no_thanks'              => 'Нет, спасибо',
        'not_null'               => 'Не Null',
        'options'                => 'Параметры',
        'password'               => 'Пароль',
        'permissions'            => 'Права доступа',
        'profile'                => 'Профиль',
        'public_url'             => 'Общедоступный URL-адрес',
        'read'                   => 'Считывание',
        'rename'                 => 'Переименовать',
        'required'               => 'Обязательный',
        'return_to_list'         => 'Вернуться к списку',
        'route'                  => 'Маршрут',
        'save'                   => 'Сохранить',
        'search'                 => 'Искать',
        'select_all'             => 'Выбрать все',
        'settings'               => 'Настройки',
        'showing_entries'        => 'Показана от :from до :to из :all запись|Показано от :from до :to из :all записей',
        'submit'                 => 'Отправить',
        'successfully_added_new' => 'Успешное добавление',
        'successfully_deleted'   => 'Успешное удаление',
        'successfully_updated'   => 'Успешное обновление',
        'timestamp'              => 'Временная метка',
        'title'                  => 'Название',
        'type'                   => 'Тип',
        'unsigned'               => 'Unsigned',
        'unstick_sidebar'        => 'Открепить боковую панель',
        'update'                 => 'Обновить',
        'update_failed'          => 'Обновление не удалось',
        'upload'                 => 'Загрузка',
        'url'                    => 'URL',
        'view'                   => 'Вид',
        'viewing'                => 'Просмотр',
        'yes'                    => 'Да',
        'yes_please'             => 'Да, пожалуйста',
    ],

    'login' => [
        'loggingin'    => 'Вход в систему',
        'signin_below' => 'Вход в панель управления',
        'welcome'      => 'Панель управления, которой не хватало в Laravel',
    ],

    'profile' => [
        'avatar'        => 'Фото',
        'edit'          => 'Настройки профиля',
        'edit_user'     => 'Изменить профиль',
        'password'      => 'Пароль',
        'password_hint' => 'Для сохранения того же значения оставьте поле пустым',
        'role'          => 'Группа',
        'user_role'     => 'Группа пользователя',
    ],

    'settings' => [
        'usage_help'           => 'Чтобы получить значения параметров, используйте в шаблоне код ',
        'save'                 => 'Сохранить настройки',
        'new'                  => 'Создать настройку',
        'help_name'            => 'Название параметра (например, Мой параметр)',
        'help_key'             => 'Ключ параметра (например, my_parametr)',
        'help_option'          => '(необязательно, применяется только к выпадающему списку или радио-кнопкам)',
        'add_new'              => 'Добавить новый параметр',
        'delete_question'      => 'Вы уверены, что нужно удалить параметр :setting?',
        'delete_confirm'       => 'Да, удалите этот параметр',
        'successfully_created' => 'Параметры успешно созданы',
        'successfully_saved'   => 'Параметры успешно сохранены',
        'successfully_deleted' => 'Параметры успешно удалены',
        'already_at_top'       => 'Уже размещено вверху списка',
        'already_at_bottom'    => 'Уже размещено внизу списка',
        'moved_order_up'       => 'Параметр :name перемещен вверх',
        'moved_order_down'     => 'Параметр :name перемещен вниз',
        'successfully_removed' => 'Успешно удалено значение параметра :name',
    'group_general'            => 'Основное',
        'group_admin'          => 'Админ',
        'group_site'           => 'Сайт',
        'group'                => 'Группа',
        'help_group'           => 'Привязать эту настройку к группе',
    ],

    'media' => [
        'add_new_folder'         => 'Добавить новую папку',
        'audio_support'          => 'Ваш браузер не поддерживает элемент audio.',
        'create_new_folder'      => 'Создать новую папку',
        'delete_folder_question' => 'Удаление папки приведет к удалению всего ее содержимого.',
        'destination_folder'     => 'Папка назначения',
        'drag_drop_info'         => 'Перетащите файлы мышью или нажмите на кнопку внизу для загрузки.',
        'error_already_exists'   => 'Файл/папка с таким именем уже существуют в данном каталоге',
        'error_creating_dir'     => 'Не удалось создать папку: проверьте права доступа',
        'error_deleting_file'    => 'Не удалось удалить файл: проверьте права доступа',
        'error_deleting_folder'  => 'Не удалось удалить папку: проверьте права доступа',
        'error_may_exist'        => 'Файл или папка с таким именем уже существуют: выберите другое имя или удалите существующий файл!',
        'error_moving'           => 'Не удалось переместить файл или папку: проверьте права доступа.',
        'error_uploading'        => 'Ошибка загрузки: Произошла неизвестная ошибка!',
        'folder_exists_already'  => 'Папка с таким именем уже существует: удалите ее, если хотите создать новую с таким же именем.',
        'image_does_not_exist'   => 'Изображения не существует',
        'image_removed'          => 'Изображение удалено',
        'library'                => 'Библиотека медиа',
        'loading'                => 'ИДЕТ ЗАГРУЗКА ВАШИХ ФАЙЛОВ',
        'move_file_folder'       => 'Переместить файл/папку',
        'new_file_folder'        => 'Новое имя файла/папки',
        'new_folder_name'        => 'Новое имя папки',
        'no_files_here'          => 'Тут нет файлов',
        'no_files_in_folder'     => 'Отсутствуют файлы в данной папке',
        'nothing_selected'       => 'Ничего не выбрано',
        'rename_file_folder'     => 'Переименовать файл/папку',
        'success_uploaded_file'  => 'Успешная загрузка файла!',
        'success_uploading'      => 'Успешная загрузка изображения!',
        'uploading_wrong_type'   => 'Ошибка загрузки: неподдерживаемый формат файла или слишком большой размер файла для загрузки!',
        'video_support'          => 'Ваш браузер не поддерживает элемент video.',
        'crop'                   => 'Обрезать',
        'crop_and_create'        => 'Создать и Обрезать',
        'crop_override_confirm'  => 'Исходное изображение будет изменено, вы уверены?',
        'crop_image'             => 'Обрезать изображение',
        'success_crop_image'     => 'Изображение успешно обрезано',
        'height'                 => 'Высота: ',
        'width'                  => 'Ширина: ',
    ],

    'menu_builder' => [
        'color'                => 'Цвет в RGB или hex (необязательно)',
        'color_ph'             => 'Цвет (например, #ffffff или rgb(255, 255, 255)',
        'create_new_item'      => 'Создать новый пункт меню',
        'delete_item_confirm'  => 'Да, удалить этот пункт меню',
        'delete_item_question' => 'Вы уверены, что хотите удалить этот пункт меню?',
        'drag_drop_info'       => 'Перетащите пункты меню ниже, чтобы изменить их порядок.',
        'dynamic_route'        => 'Динамический путь',
        'edit_item'            => 'Редактировать пункт меню',
        'icon_class'           => 'Иконка для пункта меню (Используйте ',
        'icon_class2'          => 'Voyager Font Class</a>)',
        'icon_class_ph'        => 'Иконка (необязательно)',
        'item_route'           => 'Путь для пункта меню',
        'item_title'           => 'Название пункта меню',
        'link_type'            => 'Тип ссылки',
        'new_menu_item'        => 'Новый пункт меню',
        'open_in'              => 'Открыть в',
        'open_new'             => 'Новая вкладка/окно',
        'open_same'            => 'Та же вкладка/окно',
        'route_parameter'      => 'Параметры пути (если есть)',
        'static_url'           => 'Статический URL',
        'successfully_created' => 'Пункт меню успешно создан.',
        'successfully_deleted' => 'Пункт меню успешно удален.',
        'successfully_updated' => 'Пункт меню успешно обновлен.',
        'updated_order'        => 'Структура меню успешно обновлена.',
        'url'                  => 'URL для пункта меню',
        'usage_hint'           => 'Вы можете вывести меню в любом месте вашего сайта, вызвав |Вы можете вывести это меню в любом месте вашего сайта, вызвав ',
    ],

    'post' => [
        'category'         => 'Категория сообщения',
        'content'          => 'Текст сообщения',
        'details'          => 'Свойства',
        'excerpt'          => 'Анонс <small>Краткое описание статьи</small>',
        'image'            => 'Изображение',
        'meta_description' => 'Описание (meta)',
        'meta_keywords'    => 'Ключевые слова (meta)',
        'new'              => 'Опубликовать',
        'seo_content'      => 'SEO текст',
        'seo_title'        => 'SEO название',
        'slug'             => 'Ссылка',
        'status'           => 'Статус публикации',
        'status_draft'     => 'Черновик',
        'status_pending'   => 'На модерации',
        'status_published' => 'Опубликовано',
        'title'            => 'Заголовок',
        'title_sub'        => 'Название статьи',
        'update'           => 'Обновить',
    ],

    'database' => [
        'add_bread'                 => 'Добавить BREAD к данной таблице',
        'add_new_column'            => 'Добавить новый столбец',
        'add_softdeletes'           => 'Добавить Soft Deletes',
        'add_timestamps'            => 'Добавить метки времени',
        'already_exists'            => 'уже существует',
        'already_exists_table'      => 'Таблица :table уже существует',
        'bread_crud_actions'        => 'BREAD/CRUD действия',
        'bread_info'                => 'BREAD информация',
        'column'                    => 'Столбец',
        'composite_warning'         => 'Предупреждение: этот столбец является частью составного индекса',
        'controller_name'           => 'Имя контроллера',
        'controller_name_hint'      => 'например, пустой PageController,  будет использовать BREAD Controller',
        'create_bread_for_table'    => 'Создать BREAD для таблицы :table',
        'create_migration'          => 'Создать миграцию для данной таблицы?',
        'create_model_table'        => 'Создать модель для данной таблицы?',
        'create_new_table'          => 'Создать новую таблицу',
        'create_your_new_table'     => 'Создать новую таблицу',
        'default'                   => 'По умолчанию',
        'delete_bread'              => 'Удалить BREAD',
        'delete_bread_before_table' => 'Перед удалением таблицы обязательно удалите BREAD таблицы.',
        'delete_table_bread_conf'   => 'Да, удалить BREAD',
        'delete_table_bread_quest'  => 'Вы уверены, что хотите удалить BREAD для таблицы :table?',
        'delete_table_confirm'      => 'Да, удалить таблицу',
        'delete_table_question'     => 'Вы точно хотите удалить таблицу :table?',
        'description'               => 'Описание',
        'display_name'              => 'Отображаемое имя',
        'display_name_plural'       => 'Отображаемое имя (во множественном числе)',
        'display_name_singular'     => 'Отображаемое имя (в единственном числе)',
        'edit_bread'                => 'Редактировать BREAD',
        'edit_bread_for_table'      => 'Редактировать BREAD для таблицы :table',
        'edit_rows'                 => 'Редактировать строки таблицы :table ниже',
        'edit_table'                => 'Редактировать таблицу :table ниже',
        'edit_table_not_exist'      => 'Таблицы, которую вы хотите редактировать, не существует',
        'error_creating_bread'      => 'Похоже, возникла проблема с созданием данного BREAD',
        'error_removing_bread'      => 'Похоже, возникла проблема с удалением данного BREAD',
        'error_updating_bread'      => 'Похоже, возникла проблема с обновлением данного BREAD',
        'extra'                     => 'Дополнительно',
        'field'                     => 'Поле',
        'field_safe_failed'         => 'Не удалось сохранить поле :field, будет произведен откат к предыдущему значению.',
        'generate_permissions'      => 'Создание прав доступа',
        'icon_class'                => 'Значок для данной таблицы',
        'icon_hint'                 => 'Значок для (необязательно)',
        'icon_hint2'                => 'Voyager Font Class',
        'index'                     => 'INDEX',
        'input_type'                => 'Тип ввода',
        'key'                       => 'Ключ',
        'model_class'               => 'Название класса модели',
        'model_name'                => 'Название модели',
        'model_name_ph'             => 'например \App\Models\User, если оставить пустым - попытается использовать название таблицы',
        'name_warning'              => 'Укажите столбец перед добавлением индекса',
        'no_composites_warning'     => 'В данной таблице присутствует составной индекс. Обратите внимание, что в настоящий момент они не поддерживаются. Будьте осторожны при попытке добавить/удалить индексы.',
        'null'                      => 'Null',
        'optional_details'          => 'Дополнительные сведения',
        'policy_class'              => 'Имя класса политики',
        'policy_name'               => 'Политика',
        'policy_name_ph'            => 'например \App\Policies\UserPolicy, если оставить пустым - попытается использовать политику по умолчанию',
        'primary'                   => 'ПЕРВИЧНЫЙ КЛЮЧ',
        'server_pagination'         => 'Пагинация на стороне сервера',
        'success_create_table'      => 'Таблица :table успешно создана',
        'success_created_bread'     => 'Новый BREAD успешно создан',
        'success_delete_table'      => 'Таблица :table успешно удалена',
        'success_remove_bread'      => 'BREAD успешно удален из :datatype',
        'success_update_bread'      => 'BREAD успешно обновлен в :datatype',
        'success_update_table'      => 'Таблица :table успешно обновлена',
        'table_actions'             => 'Действия с таблицей',
        'table_columns'             => 'Столбцы таблицы',
        'table_has_index'           => 'В данной таблице уже имеется первичный ключ.',
        'table_name'                => 'Название таблицы',
        'table_no_columns'          => 'В таблице отсутствуют столбцы...',
        'type'                      => 'Тип',
        'type_not_supported'        => 'Данный тип не поддерживается',
        'unique'                    => 'UNIQUE',
        'unknown_type'              => 'Неизвестный тип',
        'update_table'              => 'Обновить таблицу',
        'url_slug'                  => 'URL Slug (должен быть уникальным)',
        'url_slug_ph'               => 'URL slug (например, posts)',
        'visibility'                => 'Видимость',
    ],

    'dimmer' => [
        'page'           => 'страница|страницы',
        'page_link_text' => 'Все страницы',
        'page_text'      => 'В базе данных :count :string',
        'post'           => 'запись|записи',
        'post_link_text' => 'Все записи',
        'post_text'      => 'В базе данных :count :string',
        'user'           => 'пользователь|пользователей',
        'user_link_text' => 'Все пользователи',
        'user_text'      => 'В базе данных :count :string',
    ],

    'form' => [
        'field_password_keep'          => 'Оставьте пустым, если не хотите менять пароль',
        'field_select_dd_relationship' => 'Обязательно настройте соответствующие отношения (relationship) в методе :method класса :class.',
        'type_checkbox'                => 'Чекбокс',
        'type_codeeditor'              => 'Редактор кода',
        'type_file'                    => 'Файл',
        'type_image'                   => 'Изображение',
        'type_radiobutton'             => 'Радио-кнопка',
        'type_richtextbox'             => 'Визуальный редактор',
        'type_selectdropdown'          => 'Выпадающий список',
        'type_textarea'                => 'Текстовое поле',
        'type_textbox'                 => 'Поле ввода',
    ],

    // DataTable translations from: https://github.com/DataTables/Plugins/tree/master/i18n
    'datatable' => [
        'sEmptyTable'     => 'В таблице нет данных',
        'sInfo'           => 'Показано от _START_ до _END_ из _TOTAL_ записей',
        'sInfoEmpty'      => 'Показано 0 из 0 записей',
        'sInfoFiltered'   => '(выбрано из _MAX_ записей)',
        'sInfoPostFix'    => '',
        'sInfoThousands'  => ',',
        'sLengthMenu'     => 'Показать _MENU_ записей',
        'sLoadingRecords' => 'Загрузка записей...',
        'sProcessing'     => 'Подождите...',
        'sSearch'         => 'Поиск:',
        'sZeroRecords'    => 'Записи отсутствуют',
        'oPaginate'       => [
            'sFirst'    => 'Первая',
            'sLast'     => 'Последняя',
            'sNext'     => 'Следующая',
            'sPrevious' => 'Предыдущая',
        ],
        'oAria' => [
            'sSortAscending'  => ': активировать для сортировки столбца по возрастанию',
            'sSortDescending' => ': активировать для сортировки столбца по убыванию',
        ],
    ],

    'theme' => [
        'footer_copyright'  => 'Сделано с <i class="voyager-heart"></i> ',
        'footer_copyright2' => 'Сделано под ромом :) ',
    ],

    'json' => [
        'invalid'           => 'неверный формат JSON',
        'invalid_message'   => 'Введен неверный формат JSON',
        'valid'             => 'Верный формат JSON',
        'validation_errors' => 'Ошибки при проверке данных',
    ],

    'analytics' => [
        'by_pageview'            => 'По страницам',
        'by_sessions'            => 'По сессиям',
        'by_users'               => 'По пользователям',
        'no_client_id'           => 'Для активации аналитики необходимо получить идентификатор клиента Google Analytics и добавить его в поле <code>google_analytics_client_id</code> меню настроек. Получить код Google Analytics: ',
        'set_view'               => 'Выберите вид',
        'this_vs_last_week'      => 'Текущая неделя в сравнении с прошлой.',
        'this_vs_last_year'      => 'Нынешний год в сравнении с прошлым',
        'top_browsers'           => 'Лучшие браузеры',
        'top_countries'          => 'Лучшие страны',
        'various_visualizations' => 'Различные визуализации',
    ],

    'error' => [
        'symlink_created_text'   => 'Мы создали ссылку для вас.',
        'symlink_created_title'  => 'Создана недостающая ссылка на хранилище данных.',
        'symlink_failed_text'    => 'Не удалось создать недостающую ссылку: похоже, дело в хостинге.',
        'symlink_failed_title'   => 'Не удалось создать ссылку для хранилища данных.',
        'symlink_missing_button' => 'Исправьте',
        'symlink_missing_text'   => 'Не найдена ссылка на хранилище данных: это может вызвать проблемы с загрузкой медиафайлов.',
        'symlink_missing_title'  => 'Отсутствует ссылка на хранилище данных.',
    ],
];
