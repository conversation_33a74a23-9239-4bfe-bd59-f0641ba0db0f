<?php

namespace App\Filament\Resources\ChargeConfigResource\Pages;

use App\Filament\Resources\ChargeConfigResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListChargeConfigs extends ListRecords
{
    protected static string $resource = ChargeConfigResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}
