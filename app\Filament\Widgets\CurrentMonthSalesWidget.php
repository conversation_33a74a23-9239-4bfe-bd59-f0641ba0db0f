<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class CurrentMonthSalesWidget extends BaseWidget
{
    protected static ?int $sort = 100;

    protected function getStats(): array
    {
        $now = Carbon::now();
        $currentMonth = $now->copy()->startOfMonth();
        $endOfMonth = $now->copy()->endOfMonth();

        // Vendas do mês atual
        $currentMonthSales = Sale::whereBetween('sale_date', [$currentMonth, $endOfMonth])->get();

        $totalQuantity = $currentMonthSales->sum('quantity');
        $totalValue = $currentMonthSales->sum('total');

        // Vendas do mês anterior para comparação
        $previousMonth = $now->copy()->startOfMonth()->subMonth();
        $endOfPreviousMonth = $previousMonth->copy()->endOfMonth();

        $previousMonthSales = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])->get();
        $previousTotalQuantity = $previousMonthSales->sum('quantity');
        $previousTotalValue = $previousMonthSales->sum('total');

        // Calcular variação percentual
        $quantityChange = $previousTotalQuantity > 0
            ? (($totalQuantity - $previousTotalQuantity) / $previousTotalQuantity) * 100
            : 0;

        $valueChange = $previousTotalValue > 0
            ? (($totalValue - $previousTotalValue) / $previousTotalValue) * 100
            : 0;

        return [
            Stat::make('Quantidade de Vendas - Mês Atual', number_format($totalQuantity, 0, ',', '.'))
                ->description($quantityChange >= 0
                    ? '+' . number_format($quantityChange, 1) . '% em relação ao mês anterior'
                    : number_format($quantityChange, 1) . '% em relação ao mês anterior'
                )
                ->descriptionIcon($quantityChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($quantityChange >= 0 ? 'success' : 'danger')
                ->chart([7, 2, 10, 3, 15, 4, 17]),

            Stat::make('Valor Total - Mês Atual', 'R$ ' . number_format($totalValue, 2, ',', '.'))
                ->description($valueChange >= 0
                    ? '+' . number_format($valueChange, 1) . '% em relação ao mês anterior'
                    : number_format($valueChange, 1) . '% em relação ao mês anterior'
                )
                ->descriptionIcon($valueChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($valueChange >= 0 ? 'success' : 'danger')
                ->chart([7, 2, 10, 3, 15, 4, 17]),
        ];
    }
}
