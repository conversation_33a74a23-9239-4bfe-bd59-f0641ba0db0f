<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('charge_configs', function (Blueprint $table) {
            $table->id();
            $table->string('pix_name', 100);
            $table->string('pix_key', 191);
            $table->string('pix_type', 30);
            $table->string('pix_bank', 100);
            $table->text('whatsapp_template');
            $table->timestamps();
        });

        DB::table('charge_configs')->insert([
            'pix_name' => 'Meu nome',
            'pix_key' => 'Minha chave Pix',
            'pix_type' => 'random',
            'pix_bank' => 'Banco da chave Pix',
            'whatsapp_template' => 'Olá! Tudo bem?',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('charge_configs');
    }
};
