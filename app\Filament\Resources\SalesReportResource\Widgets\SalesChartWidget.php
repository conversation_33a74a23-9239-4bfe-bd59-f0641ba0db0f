<?php

namespace App\Filament\Resources\SalesReportResource\Widgets;

use App\Models\Sale;
use Filament\Widgets\ChartWidget;

class SalesChartWidget extends ChartWidget
{
    protected static ?string $heading = 'Vendas por Mês - Valor e Quantidade';
    protected static ?int $sort = 2;

    protected function getData(): array
    {
        // Pegar dados dos últimos 12 meses
        $salesData = [];
        $quantityData = [];
        $labels = [];

        for ($i = 11; $i >= 0; $i--) {
            $date = now()->startOfMonth()->subMonths($i);

            // Buscar vendas e quantidade em uma única consulta
            $monthData = Sale::whereMonth('sale_date', $date->month)
                ->whereYear('sale_date', $date->year)
                ->selectRaw('SUM(total) as total_sales, COUNT(*) as total_count')
                ->first();

            $salesData[] = (float) ($monthData->total_sales ?? 0);
            $quantityData[] = (int) ($monthData->total_count ?? 0);
            $labels[] = $this->getMonthLabel($date);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Valor das Vendas (R$)',
                    'data' => $salesData,
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'borderColor' => 'rgb(59, 130, 246)',
                    'borderWidth' => 2,
                    'fill' => true,
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Quantidade de Vendas',
                    'data' => $quantityData,
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'borderColor' => 'rgb(34, 197, 94)',
                    'borderWidth' => 2,
                    'fill' => false,
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Valor (R$)',
                    ],
                    'ticks' => [
                        'callback' => 'function(value) { return "R$ " + value.toLocaleString("pt-BR"); }',
                    ],
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'beginAtZero' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Quantidade',
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                    'ticks' => [
                        'callback' => 'function(value) { return value + " vendas"; }',
                    ],
                ],
            ],
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }

    private function getMonthLabel($date): string
    {
        $monthsInPortuguese = [
            1 => 'Jan',
            2 => 'Fev',
            3 => 'Mar',
            4 => 'Abr',
            5 => 'Mai',
            6 => 'Jun',
            7 => 'Jul',
            8 => 'Ago',
            9 => 'Set',
            10 => 'Out',
            11 => 'Nov',
            12 => 'Dez',
        ];

        return $monthsInPortuguese[$date->month] . '/' . $date->year;
    }
}
