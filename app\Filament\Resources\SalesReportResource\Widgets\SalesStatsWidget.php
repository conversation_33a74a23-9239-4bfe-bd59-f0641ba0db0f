<?php

namespace App\Filament\Resources\SalesReportResource\Widgets;

use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class SalesStatsWidget extends BaseWidget
{
    protected function getStats(): array
    {
        // Estatísticas gerais
        $totalSales = Sale::sum('total');
        $totalQuantity = Sale::sum('quantity');
        $totalSalesCount = Sale::count();

        // Vendas pendentes
        $pendingSales = Sale::where('paid', false)->sum('total');

        // Estatísticas do mês atual
        $currentMonth = now();
        $thisMonthSales = Sale::whereMonth('sale_date', $currentMonth->month)
            ->whereYear('sale_date', $currentMonth->year)
            ->sum('total');

        // Estatísticas do mês anterior para comparação
        $lastMonth = now()->startOfMonth()->subMonth();
        $lastMonthSales = Sale::whereMonth('sale_date', $lastMonth->month)
            ->whereYear('sale_date', $lastMonth->year)
            ->sum('total');

        // Calcular crescimento
        $growth = $lastMonthSales > 0
            ? (($thisMonthSales - $lastMonthSales) / $lastMonthSales) * 100
            : 0;

        // Cliente com mais vendas
        $topCustomer = Sale::select('person_id', DB::raw('SUM(total) as total_sales'))
            ->groupBy('person_id')
            ->orderBy('total_sales', 'desc')
            ->with('person')
            ->first();



        return [
            Stat::make('Total de Vendas', 'R$ ' . number_format($totalSales, 2, ',', '.'))
                ->description('Valor total acumulado')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('success'),

            Stat::make('Vendas Este Mês', 'R$ ' . number_format($thisMonthSales, 2, ',', '.'))
                ->description($growth >= 0
                    ? '+' . number_format($growth, 1) . '% em relação ao mês anterior'
                    : number_format($growth, 1) . '% em relação ao mês anterior')
                ->descriptionIcon($growth >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($growth >= 0 ? 'success' : 'danger'),

            Stat::make('Vendas Pendentes', 'R$ ' . number_format($pendingSales, 2, ',', '.'))
                ->description('Aguardando pagamento')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),

            Stat::make('Total de Transações', number_format($totalSalesCount, 0, ',', '.'))
                ->description('Número total de vendas')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('primary'),

            Stat::make('Quantidade Total', number_format($totalQuantity, 0, ',', '.'))
                ->description('Itens vendidos')
                ->descriptionIcon('heroicon-m-cube')
                ->color('info'),

            Stat::make('Melhor Cliente', $topCustomer ? $topCustomer->person->name : 'N/A')
                ->description($topCustomer
                    ? 'R$ ' . number_format($topCustomer->total_sales, 2, ',', '.')
                    : 'Nenhuma venda')
                ->descriptionIcon('heroicon-m-user')
                ->color('success'),
        ];
    }

    protected function getColumns(): int
    {
        return 3;
    }
}
