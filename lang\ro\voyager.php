<?php

return [
    'date' => [
        'last_week' => 'Săptămâna trecută',
        'last_year' => 'Anul trecut',
        'this_week' => 'Săptămâna asta',
        'this_year' => 'În acest an',
    ],

    'generic' => [
        'action'                 => 'Acțiune',
        'actions'                => 'Acțiuni',
        'add'                    => 'Adaugă',
        'add_folder'             => 'Crează folder',
        'add_new'                => 'Adaugă nou',
        'all_done'               => 'Gata',
        'are_you_sure'           => 'Sunteți sigur',
        'are_you_sure_delete'    => 'Sunteți sigur că doriți să ștergeți',
        'auto_increment'         => 'Auto incrementare',
        'browse'                 => 'Răsfoiește',
        'builder'                => 'Constructor',
    'bulk_delete'                => 'Șterge tot',
        'bulk_delete_confirm'    => 'Da, șterge asta',
        'bulk_delete_nothing'    => 'Nu ați ales nimic pentru ștergere',
        'cancel'                 => 'Anulare',
        'choose_type'            => 'Alegeți tipul',
        'click_here'             => 'Click aici',
        'close'                  => 'Închide',
    'compass'                    => 'Busolă',
        'created_at'             => 'Data creării',
        'custom'                 => 'Personalizat',
        'dashboard'              => 'Panou de control',
        'database'               => 'Baza de date',
        'default'                => 'Prestabilit',
        'delete'                 => 'Șterge',
        'delete_confirm'         => 'Da, Șterge',
        'delete_question'        => 'Sunteți sigur că vreți să ștergeți asta',
        'delete_this_confirm'    => 'Da, șterge asta',
        'deselect_all'           => 'Anulează selecția',
        'download'               => 'Descarcă',
        'edit'                   => 'Editare',
        'email'                  => 'E-mail',
        'error_deleting'         => 'A apărut o eroare în timpul ștergerii',
        'exception'              => 'Excepție',
        'featured'               => 'Recomandat',
        'field_does_not_exist'   => 'Câmpul nu există',
        'how_to_use'             => 'Cum să folosiți',
        'index'                  => 'Index',
        'internal_error'         => 'Eroare internă',
        'items'                  => 'Element(e)',
        'keep_sidebar_open'      => 'Yarr! Aruncați ancorele! (și ține-ți bara laterală deschisă)',
        'key'                    => 'Cheie',
        'last_modified'          => 'Ultima modificare',
        'length'                 => 'Lungime',
        'login'                  => 'Login',
        'media'                  => 'Media',
        'menu_builder'           => 'Constructor de meniuri',
        'move'                   => 'Mutare',
        'name'                   => 'Nume',
        'new'                    => 'Nou',
        'no'                     => 'Nu',
        'no_thanks'              => 'Nu, mulțumesc',
        'not_null'               => 'Nu-i Null',
        'options'                => 'Opțiuni',
        'password'               => 'Parolă',
        'permissions'            => 'Permisiuni',
        'profile'                => 'Profil',
        'public_url'             => 'URL public',
        'read'                   => 'Citire',
        'rename'                 => 'Redenumire',
        'required'               => 'Obligatoriu',
        'return_to_list'         => 'Întoarcere la listă',
        'route'                  => 'Traseu',
        'save'                   => 'Salvare',
        'search'                 => 'Caută',
        'select_all'             => 'Selectează tot',
        'settings'               => 'Setări',
        'showing_entries'        => 'Publicare afișată de la :from până la :to din :all|Publicări afișate de la :from până la :to din :all',
        'submit'                 => 'Trimite',
        'successfully_added_new' => 'Adăugat cu succes',
        'successfully_deleted'   => 'Șters cu succes',
        'successfully_updated'   => 'Actualizat cu succes',
        'timestamp'              => 'Timestamp-ul',
        'title'                  => 'Titlu',
        'type'                   => 'Tip',
        'unsigned'               => 'Nesemnat',
        'unstick_sidebar'        => 'Desfaceți bara laterală',
        'update'                 => 'Actualizează',
        'update_failed'          => 'Actualizare eșuată',
        'upload'                 => 'Încărcare',
        'url'                    => 'URL',
        'view'                   => 'Vedere',
        'viewing'                => 'Vizualizare',
        'yes'                    => 'Da',
        'yes_please'             => 'Da, vă rog',
    ],

    'login' => [
        'loggingin'    => 'Logare în sistem',
        'signin_below' => 'Conectați-vă mai jos:',
        'welcome'      => 'Bine ați venit la Voyager. Panoul de control ce lipsește în Laravel',
    ],

    'profile' => [
        'avatar'        => 'Poza',
        'edit'          => 'Editează profilul',
        'edit_user'     => 'Editează utilizatorul',
        'password'      => 'Parola',
        'password_hint' => 'Lăsați gol pentru a păstra aceeași',
        'role'          => 'Rol',
        'user_role'     => 'Rolul utilizatorului',
    ],

    'settings' => [
        'usage_help'           => 'Puteți folosi valoarea fiecărei setări, oriunde pe site apelând',
        'save'                 => 'Salvează setările',
        'new'                  => 'Setare nouă',
        'help_name'            => 'Numele setării (ex: Titlu Admin)',
        'help_key'             => 'Cheia setării (ex: admin_title)',
        'help_option'          => '(opțional, se aplică doar la unele tipuri, cum ar fi dropdown sau buton radio)',
        'add_new'              => 'Adăugați setare nouă',
        'delete_question'      => 'Sunteți sigur că doriți să ștergeți setarea :setting?',
        'delete_confirm'       => 'Da, șterge această setare',
        'successfully_created' => 'Setare creată cu succes',
        'successfully_saved'   => 'Setare salvată cu succes',
        'successfully_deleted' => 'Setare ștearsă cu succes',
        'already_at_top'       => 'Deja este prima în listă',
        'already_at_bottom'    => 'Deja este ultima în listă',
        'moved_order_up'       => 'Setarea :name a fost mutată mai sus',
        'moved_order_down'     => 'Setarea :name a fost mutată mai jos',
        'successfully_removed' => 'Valoarea :name a fost ștearsă cu succes',
    'group_general'            => 'General',
        'group_admin'          => 'Admin',
        'group_site'           => 'Site',
        'group'                => 'Grup',
        'help_group'           => 'Atașați această setare la grupul',
    ],

    'media' => [
        'add_new_folder'         => 'Adaugă un folder nou',
        'audio_support'          => 'Browser-ul dvs. nu suportă elementul audio.',
        'create_new_folder'      => 'Crează un folder nou',
        'delete_folder_question' => 'Ștergerea folderului va duce la ștergerea fișierelor și folderelor ce se află el.',
        'destination_folder'     => 'Folderul de destinație',
        'drag_drop_info'         => 'Trageți și aruncați fișiere.',
        'error_already_exists'   => 'Există deja fișier/folder cu așa nume în acest folder',
        'error_creating_dir'     => 'Eroare la crearea folderului: verificați permisiunile',
        'error_deleting_file'    => 'Eroare la ștergerea fișierului: verificați permisiunile',
        'error_deleting_folder'  => 'Eroare la ștergerea folderului: verificați permisiunile',
        'error_may_exist'        => 'Există deja un fișier sau un folder cu așa nume: alegeți alt nume sau ștergeți fișierul curent',
        'error_moving'           => 'Eroare la mutarea fișierului/folderului: verificați permisiunile.',
        'error_uploading'        => 'Încărcare eșuată: S-a produs o eroare necunoscută',
        'folder_exists_already'  => 'Folder cu așa nume există deja. Vă rugăm să o ștergeți dacă doriți să creați una cu același nume.',
        'image_does_not_exist'   => 'Imaginea nu există',
        'image_removed'          => 'Imagine ștearsă',
        'library'                => 'Bibliotecă media',
        'loading'                => 'SE ÎNCARCĂ FIȘIERELE DVS. MEDIA',
        'move_file_folder'       => 'Mutare fișier/folder',
        'new_file_folder'        => 'Nume nou fișier/folder',
        'new_folder_name'        => 'Nume nou folder',
        'no_files_here'          => 'Aici nu există fișiere',
        'no_files_in_folder'     => 'În acest folder nu există fișiere',
        'nothing_selected'       => 'Nimic selectat',
        'rename_file_folder'     => 'Redenumire fișier/folder',
        'success_uploaded_file'  => 'Încărcarea fișierului a avut loc cu succes',
        'success_uploading'      => 'Încărcarea imaginii a avut loc cu succes',
        'uploading_wrong_type'   => 'Încărcare eșuată: formatul fișierului nu este suportat sau fișierul este prea mare pentru a fi încărcat!',
        'video_support'          => 'Browser-ul dvs. nu suportă elementul video.',
    ],

    'menu_builder' => [
        'color'                => 'Culoarea în RGB sau hex (opțional)',
        'color_ph'             => 'Culoarea (ex: #ffffff sau rgb(255, 255, 255)',
        'create_new_item'      => 'Crează un punct de meniu nou',
        'delete_item_confirm'  => 'Da, șterge acest punct de meniu',
        'delete_item_question' => 'Sunteți sigur, că doriți să ștergeți acest punct de meniu?',
        'drag_drop_info'       => 'Trageți punctul din meniu mai jos, pentru a schimba ordinea lor.',
        'dynamic_route'        => 'Cale(route) dinamică',
        'edit_item'            => 'Editează punct de meniu',
        'icon_class'           => 'Iconiță pentru punctul de meniu (Folosiți ',
        'icon_class2'          => 'Voyager Font Class</a>)',
        'icon_class_ph'        => 'Iconiță (opțional)',
        'item_route'           => 'Calea pentru punctul de meniu',
        'item_title'           => 'Denumirea punctului de meniu',
        'link_type'            => 'Tipul link-ului',
        'new_menu_item'        => 'Punct de meniu nou',
        'open_in'              => 'Deschide în',
        'open_new'             => 'Fereastră/Tab nou',
        'open_same'            => 'aceeași fereastră/tab',
        'route_parameter'      => 'Parametrii rutei (dacă există)',
        'static_url'           => 'URL Static',
        'successfully_created' => 'Punctul de meniu a fost creat cu succes.',
        'successfully_deleted' => 'Punctul de meniu a fost șters cu succes.',
        'successfully_updated' => 'Punctul de meniu a fost actualizat cu succes.',
        'updated_order'        => 'Structura meniului a fost actualizată cu succes.',
        'url'                  => 'URL pentru punctul de meniu',
        'usage_hint'           => 'Puteți afișa un meniu oriunde pe site apelând|Puteți afișa acest meniu oriunde pe site apelând',
    ],

    'post' => [
        'category'         => 'Categoria postării',
        'content'          => 'Conținutul postării',
        'details'          => 'Detaliile postării',
        'excerpt'          => 'Extras <small>Descrierea scurtă a postării</small>',
        'image'            => 'Imagine',
        'meta_description' => 'Descriere meta',
        'meta_keywords'    => 'Cuvinte cheie',
        'new'              => 'Creați o postare nouă',
        'seo_content'      => 'Conținut SEO',
        'seo_title'        => 'Titlu SEO',
        'slug'             => 'slug(link)',
        'status'           => 'Starea postării',
        'status_draft'     => 'Ciornă',
        'status_pending'   => 'În așteptare',
        'status_published' => 'Publicat',
        'title'            => 'Titlu',
        'title_sub'        => 'Titlul postării',
        'update'           => 'Actualizarea postării',
    ],

    'database' => [
        'add_bread'                 => 'Adăugați BREAD la acest tabel',
        'add_new_column'            => 'Adăugați o coloană nouă',
        'add_softdeletes'           => 'Adăugați Soft Deletes',
        'add_timestamps'            => 'Adăugați timestamp-uri',
        'already_exists'            => 'deja există',
        'already_exists_table'      => 'Tabelul :table deja există',
        'bread_crud_actions'        => 'Acțiuni BREAD/CRUD',
        'bread_info'                => 'Informații despre BREAD',
        'column'                    => 'Coloană',
        'composite_warning'         => 'Avertizare: această coloană face parte din indexul compozit',
        'controller_name'           => 'Numele controller-ului',
        'controller_name_hint'      => 'ex: PageController, dacă lăsați liber se va folosi BREAD Controller',
        'create_bread_for_table'    => 'Creare BREAD pentru tabelul :table',
        'create_migration'          => 'Creare migrare pentru acest tabel?',
        'create_model_table'        => 'Creare model pentru acest tabel?',
        'create_new_table'          => 'Creare tabel nou',
        'create_your_new_table'     => 'Creare tabel nou',
        'default'                   => 'Prdefinit',
        'delete_bread'              => 'Șterge BREAD',
        'delete_bread_before_table' => 'Înainte de a șterge tabelul este necesar să ștergeți BREAD-ul tabelului.',
        'delete_table_bread_conf'   => 'Da, șterge BREAD',
        'delete_table_bread_quest'  => 'Sunteți sigur, că doriți să ștergeți BREAD-ul tabelului :table?',
        'delete_table_confirm'      => 'Da, șterge tabelul',
        'delete_table_question'     => 'Sunteți sigur că doriți să ștergeți tabelul :table?',
        'description'               => 'Descriere',
        'display_name'              => 'Numele afișat',
        'display_name_plural'       => 'Numele afișat (la plural)',
        'display_name_singular'     => 'Numele afișat (la singular)',
        'edit_bread'                => 'Editare BREAD',
        'edit_bread_for_table'      => 'Editare BREAD pentru tabelul :table',
        'edit_rows'                 => 'Editați rândurile tabelului :table mai jos',
        'edit_table'                => 'Editați tabelul :table mai jos',
        'edit_table_not_exist'      => 'Tabelul pe care doriți să-l editați nu există',
        'error_creating_bread'      => 'Se pare că a apărut o problemă cu crearea acestui BREAD',
        'error_removing_bread'      => 'Se pare că a apărut o problemă cu ștergerea acestui BREAD',
        'error_updating_bread'      => 'Se pare că a apărut o problemă cu actualizarea acestui BREAD',
        'extra'                     => 'Suplimentar',
        'field'                     => 'Câmp',
        'field_safe_failed'         => 'Nu s-a reușit savlarea câmpului :field, ne întoarcem la valoarea precedentă.',
        'generate_permissions'      => 'Generare permisiuni',
        'icon_class'                => 'Iconiță pentru acest tabel',
        'icon_hint'                 => 'Iconiță pentru (opțional)',
        'icon_hint2'                => 'Voyager Font Class',
        'index'                     => 'INDEX',
        'input_type'                => 'Tipul input-ului',
        'key'                       => 'Cheie',
        'model_class'               => 'Numele clasei modelului',
        'model_name'                => 'Numele modelului',
        'model_name_ph'             => 'ex: \App\Models\User, dacă lăsați gol, vom încerca și vom folosi numele tabelului',
        'name_warning'              => 'Vă rugăm să indicați numele coloanei înainte de adăugarea indexului',
        'no_composites_warning'     => 'În acest tabel există index compozit. Atrageți atenția că la momentul de față ele nu sunt suportate. Fiți atenți când încercați să adăugați/ștergeți indexuri.',
        'null'                      => 'Null',
        'optional_details'          => 'Detalii suplimentare',
        'policy_class'              => 'Policy Class Name',
        'policy_name'               => 'Policy Name',
        'policy_name_ph'            => 'ex. \App\Policies\UserPolicy, dacă lăsați gol, vom încerca și vom folosi predefinit',
    'primary'                       => 'CHEIE PRIMARĂ',
        'server_pagination'         => 'Paginare pe server',
        'success_create_table'      => 'Tabelul :table a fost creat cu succes',
        'success_created_bread'     => 'BREAD nou a fost creat cu succes',
        'success_delete_table'      => 'Tabelul :table a fost șters cu succes',
        'success_remove_bread'      => 'BREAD a fost șters cu succes din :datatype',
        'success_update_bread'      => 'BREAD a fost actualizat cu succes în :datatype',
        'success_update_table'      => 'Tabelul :table a fost actualizat cu succes',
        'table_actions'             => 'Acțiuni cu tabelul',
        'table_columns'             => 'Coloanele tabelului',
        'table_has_index'           => 'În acest tabel există deja cheia primară.',
        'table_name'                => 'Numele tabelului',
        'table_no_columns'          => 'Acest tabel nu are coloane...',
        'type'                      => 'Tip',
        'type_not_supported'        => 'Acest tip nu este suportat',
        'unique'                    => 'UNIQUE',
        'unknown_type'              => 'Tip necunoscut',
        'update_table'              => 'Actualizare tabel',
        'url_slug'                  => 'URL Slug (trebuie să fie unic)',
        'url_slug_ph'               => 'URL slug (ex:, posts)',
        'visibility'                => 'Vizibilitate',
        'relationship'              => [
          'relationship'         => 'Relație',
          'relationships'        => 'Relații',
          'has_one'              => 'Unu la unu',
          'has_many'             => 'Unu la mulți',
          'belongs_to'           => 'Mulți la unu',
          'belongs_to_many'      => 'Mulți la mulți',
          'which_column_from'    => 'Ce coloană din',
          'is_used_to_reference' => 'este folosită pentru a face referire la',
          'pivot_table'          => 'Tabel de legătură',
          'selection_details'    => 'Detaliile selecției',
          'display_the'          => 'Afișează',
          'store_the'            => 'Salvează',
          'easy_there'           => 'Ușor, Căpitane',
          'before_create'        => 'Înainte de a crea o relație ai nevoie mai întâi să creezi BREAD-ul.<br> Apoi, te întorci înapoi pentru a edita BREAD-ul și atunci vei putea adăuga o relație nouă.<br> Mulțam.',
          'cancel'               => 'Anulare',
          'add_new'              => 'Adăugare relație nouă',
          'open'                 => 'Deschide',
          'close'                => 'Închide',
          'relationship_details' => 'Detaliile relației',
          'browse'               => 'Răsfoiește',
          'read'                 => 'Citește',
          'edit'                 => 'Editează',
          'add'                  => 'Adaugă',
          'delete'               => 'Șterge',
          'create'               => 'Crează o Relație',
          'namespace'            => 'Model Năimspăis (ex: App\Models\User)',
      ],
    ],

    'dimmer' => [
        'page'           => 'pagină|pagini',
        'page_link_text' => 'Toate paginile',
        'page_text'      => 'În baza de date există :count :string',
        'post'           => 'postare|postări',
        'post_link_text' => 'Toate postările',
        'post_text'      => 'În baza de date există :count :string',
        'user'           => 'utilizator|utilizatori',
        'user_link_text' => 'Toți utilizatorii',
        'user_text'      => 'În baza de date există :count :string',
    ],

    'form' => [
        'field_password_keep'          => 'Lăsați gol, dacă nu doriți să schimbați parola',
        'field_select_dd_relationship' => 'Este necesar să setați realțiile (relationship) în metoda :method din clasa :class.',
        'type_checkbox'                => 'Checkbox',
        'type_codeeditor'              => 'Editor de cod',
        'type_file'                    => 'Fișier',
        'type_image'                   => 'Imagine',
        'type_radiobutton'             => 'Radio buton',
        'type_richtextbox'             => 'Edito vizual',
        'type_selectdropdown'          => 'Listă dropdown',
        'type_textarea'                => 'Câmp text (textarea)',
        'type_textbox'                 => 'Câmp text (simplu)',
    ],

    // DataTable translations from: https://github.com/DataTables/Plugins/tree/master/i18n
    'datatable' => [
        'sEmptyTable'     => 'În tabel nu există date',
        'sInfo'           => 'Afișat de la _START_ până la _END_ din _TOTAL_ înregistrări',
        'sInfoEmpty'      => 'Afișat 0 din 0 înregistrări',
        'sInfoFiltered'   => '(sortat din _MAX_ înregitrări)',
        'sInfoPostFix'    => '',
        'sInfoThousands'  => ',',
        'sLengthMenu'     => 'Afișați _MENU_ înregistrări',
        'sLoadingRecords' => 'Încărcare înregistrări...',
        'sProcessing'     => 'Așteptați...',
        'sSearch'         => 'Căutare:',
        'sZeroRecords'    => 'Lipsesc înregistrări',
        'oPaginate'       => [
            'sFirst'    => 'Prima',
            'sLast'     => 'Ultima',
            'sNext'     => 'Următoarea',
            'sPrevious' => 'Precedenta',
        ],
        'oAria' => [
            'sSortAscending'  => ': activați pentru a sorta coloana crescător',
            'sSortDescending' => ': activați pentru a sorta coloana descrescător',
        ],
    ],

    'theme' => [
        'footer_copyright'  => 'Creat cu <i class="voyager-heart"></i> ',
        'footer_copyright2' => 'Creat cu rom și chiar mai mult rom :) ',
    ],

    'json' => [
        'invalid'           => 'format JSON invalid',
        'invalid_message'   => 'Ați introdus un format JSON invalid',
        'valid'             => 'Format JSON corect',
        'validation_errors' => 'Eroare la verificarea datelor',
    ],

    'analytics' => [
        'by_pageview'            => 'După pagini',
        'by_sessions'            => 'După sesiuni',
        'by_users'               => 'După utilizatori',
        'no_client_id'           => 'Pentru a vedea statisticile din analytics aveți nevoie de google analytics cliend id pe care să-l adăugați în setări pentru cheia <code>google_analytics_client_id</code>. Puteți obține cheia(analytics cliend id) în contul dvs. Google developers console:',
        'set_view'               => 'Alegeți modul de vizualizare',
        'this_vs_last_week'      => 'Săptămâna aceasta în comparație cu săptămâna trecută.',
        'this_vs_last_year'      => 'Anul acesta în comparație cu anul trecut',
        'top_browsers'           => 'Top browser-e',
        'top_countries'          => 'Top țări',
        'various_visualizations' => 'Vizualizări diverse',
    ],

    'error' => [
        'symlink_created_text'   => 'Noi tocmai am creat legătura simbolică(symlink) pentru dvs.',
        'symlink_created_title'  => 'Legătura simbolică a folderului storage ce lipsea, a fost creată.',
        'symlink_failed_text'    => 'Nu am putut genera link-ul simbolic ce lipsește pentru aplicația dvs. Se pare că hosting provider-ul dvs. nu suportă symlinks))).',
        'symlink_failed_title'   => 'Nu am putut crea link-ul simbolic pentru folderul storage.',
        'symlink_missing_button' => 'Corectați',
        'symlink_missing_text'   => 'Nu am putut găsi un link simbolic pentru folderul storage. Aceasta poate cauza probleme cu încărcarea fișierelor media de către browser.',
        'symlink_missing_title'  => 'Lipsește link-ul simbolic pentru folderul storage.',
    ],
];
