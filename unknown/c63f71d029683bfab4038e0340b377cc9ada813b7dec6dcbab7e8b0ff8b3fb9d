<?php

namespace App\Filament\Resources\SalesReportResource\Widgets;

use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class TopCustomersWidget extends BaseWidget
{
    protected static ?int $sort = 4;
    protected int | string | array $columnSpan = 'full';

    protected function getStats(): array
    {
        // Pegar os 5 melhores clientes
        $topCustomers = Sale::select('person_id', DB::raw('SUM(total) as total_sales'), DB::raw('COUNT(*) as total_orders'))
            ->with('person')
            ->groupBy('person_id')
            ->orderBy('total_sales', 'desc')
            ->limit(5)
            ->get();

        $stats = [];

        foreach ($topCustomers as $index => $customer) {
            $position = $index + 1;
            $customerName = $customer->person->name ?? 'Cliente não encontrado';
            $totalSales = $customer->total_sales;
            $totalOrders = $customer->total_orders;
            $averageOrder = $totalOrders > 0 ? $totalSales / $totalOrders : 0;

            $stats[] = Stat::make("#{$position} - {$customerName}", 'R$ ' . number_format($totalSales, 2, ',', '.'))
                ->description($totalOrders . ' pedidos | Ticket médio: R$ ' . number_format($averageOrder, 2, ',', '.'))
                ->descriptionIcon('heroicon-m-user')
                ->color($position <= 3 ? 'success' : 'primary');
        }

        // Se não houver clientes suficientes, preencher com placeholders
        while (count($stats) < 5) {
            $position = count($stats) + 1;
            $stats[] = Stat::make("#{$position} - Sem dados", 'R$ 0,00')
                ->description('Nenhuma venda registrada')
                ->descriptionIcon('heroicon-m-user')
                ->color('gray');
        }

        return $stats;
    }

    protected function getColumns(): int
    {
        return 1;
    }

    protected function getHeading(): ?string
    {
        return 'Top 5 Clientes';
    }
}
