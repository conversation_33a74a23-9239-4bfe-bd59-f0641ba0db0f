<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SalesReportResource\Pages;
use App\Models\Sale;
use App\Models\Person;
use App\Models\Product;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Indicator;
use Illuminate\Database\Eloquent\Builder;

class SalesReportResource extends Resource
{
    protected static ?string $model = Sale::class;

    protected static ?string $label = 'Relatório de Vendas';
    protected static ?string $pluralModelLabel = 'Relatórios de Vendas';

    protected static ?string $navigationIcon = 'phosphor-chart-bar';
    protected static ?string $navigationGroup = 'Relatórios';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Não precisamos de formulário para relatórios
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),

                TextColumn::make('person.name')
                    ->label('Cliente')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('product.name')
                    ->label('Produto')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('quantity')
                    ->label('Quantidade')
                    ->sortable()
                    ->alignCenter(),

                TextColumn::make('unit_price')
                    ->label('Preço Unitário')
                    ->money('BRL')
                    ->sortable(),

                TextColumn::make('total')
                    ->label('Total')
                    ->money('BRL')
                    ->sortable()
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->money('BRL')
                            ->label('Total Geral'),
                    ]),

                TextColumn::make('paid')
                    ->label('Status')
                    ->badge()
                    ->color(fn ($state): string => match ($state) {
                        true, 1, '1' => 'success',
                        false, 0, '0' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn ($state): string => match ($state) {
                        true, 1, '1' => 'Pago',
                        false, 0, '0' => 'Pendente',
                        default => 'Indefinido',
                    }),

                TextColumn::make('sale_date')
                    ->label('Data da Venda')
                    ->date('d/m/Y')
                    ->sortable(),

                TextColumn::make('payment_date')
                    ->label('Data do Pagamento')
                    ->date('d/m/Y')
                    ->placeholder('Não pago')
                    ->sortable(),
            ])
            ->filters([
                SelectFilter::make('person_id')
                    ->label('Cliente')
                    ->options(Person::all()->pluck('name', 'id'))
                    ->searchable(),

                SelectFilter::make('product_id')
                    ->label('Produto')
                    ->options(Product::all()->pluck('name', 'id'))
                    ->searchable(),

                SelectFilter::make('paid')
                    ->label('Status do Pagamento')
                    ->options([
                        1 => 'Pago',
                        0 => 'Pendente',
                    ]),

                Filter::make('sale_period')
                    ->form([
                        Forms\Components\Select::make('sale_month')
                            ->label('Mês')
                            ->options([
                                1 => 'Janeiro',
                                2 => 'Fevereiro',
                                3 => 'Março',
                                4 => 'Abril',
                                5 => 'Maio',
                                6 => 'Junho',
                                7 => 'Julho',
                                8 => 'Agosto',
                                9 => 'Setembro',
                                10 => 'Outubro',
                                11 => 'Novembro',
                                12 => 'Dezembro',
                            ])
                            ->placeholder('Selecione o mês'),

                        Forms\Components\Select::make('sale_year')
                            ->label('Ano')
                            ->options(self::getFilterYears())
                            ->placeholder('Selecione o ano'),
                    ])
                    ->query(
                        fn(Builder $query, array $data): Builder =>
                        $query
                            ->when(
                                $data['sale_month'],
                                fn(Builder $query, $value): Builder => $query->whereMonth('sale_date', $value),
                            )
                            ->when(
                                $data['sale_year'],
                                fn(Builder $query, $value): Builder => $query->whereYear('sale_date', $value),
                            )
                    )
                    ->indicateUsing(function (array $data): array {
                        if ($data['sale_month'] && $data['sale_year']) {
                            $monthName = Carbon::create()->month((int)$data['sale_month'])->translatedFormat('F');
                            $year = $data['sale_year'];

                            return [
                                Indicator::make("Período: $monthName de $year")->color('primary'),
                            ];
                        }

                        if ($data['sale_month']) {
                            $monthName = Carbon::create()->month((int)$data['sale_month'])->translatedFormat('F');
                            return [
                                Indicator::make("Mês: $monthName")->color('primary'),
                            ];
                        }

                        if ($data['sale_year']) {
                            return [
                                Indicator::make("Ano: {$data['sale_year']}")->color('primary'),
                            ];
                        }

                        return [];
                    }),

                Filter::make('date_range')
                    ->form([
                        Forms\Components\DatePicker::make('date_from')
                            ->label('Data Inicial'),
                        Forms\Components\DatePicker::make('date_to')
                            ->label('Data Final'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['date_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('sale_date', '>=', $date),
                            )
                            ->when(
                                $data['date_to'],
                                fn (Builder $query, $date): Builder => $query->whereDate('sale_date', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];

                        if ($data['date_from']) {
                            $indicators[] = Indicator::make('De: ' . Carbon::parse($data['date_from'])->format('d/m/Y'))
                                ->color('primary');
                        }

                        if ($data['date_to']) {
                            $indicators[] = Indicator::make('Até: ' . Carbon::parse($data['date_to'])->format('d/m/Y'))
                                ->color('primary');
                        }

                        return $indicators;
                    }),
            ])
            ->actions([
                // Não precisamos de ações para relatórios
            ])
            ->bulkActions([
                // Não precisamos de ações em massa para relatórios
            ])
            ->defaultSort('sale_date', 'desc')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }

    public static function getFilterYears(): array
    {
        $first = Sale::orderBy('sale_date', 'asc')->first();

        if (!$first) {
            return [date('Y') => date('Y')];
        }

        $years = [];
        $year = Carbon::create($first->sale_date)->year;

        while ($year <= date('Y')) {
            $years[$year] = $year;
            $year++;
        }

        return $years;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSalesReports::route('/'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return false;
    }
}
