# Relatório de Vendas - Sistema Sucos

## Descrição

Foi criado um sistema completo de relatórios de vendas integrado ao Filament Admin Panel, permitindo visualizar e analisar as vendas com diversos filtros e métricas.

## Funcionalidades Implementadas

### 1. Página Principal do Relatório
- **Localização**: `/admin/sales-reports`
- **Navegação**: Menu "Relatórios" > "Relatório de Vendas"
- **Ícone**: Gráfic<PERSON> de barras (phosphor-chart-bar)

### 2. Filtros Disponíveis

#### Filtros Básicos:
- **Cliente**: Seleção por cliente específico
- **Produto**: Seleção por produto específico  
- **Status do Pagamento**: Pago ou Pendente

#### Filtros de Período:
- **Mês/Ano**: Seleção específica de mês e ano
- **Intervalo de Datas**: Data inicial e final personalizadas

### 3. Colunas da Tabela
- ID da venda
- Nome do cliente
- Nome do produto
- Quantidade vendida
- Preço unitário (em R$)
- Total da venda (em R$)
- Status do pagamento (badge colorido)
- Data da venda
- Data do pagamento

### 4. Abas de Navegação Rápida
- **Todas as Vendas**: Visualização completa
- **Vendas Pagas**: Apenas vendas quitadas
- **Vendas Pendentes**: Vendas aguardando pagamento
- **Este Mês**: Vendas do mês atual
- **Mês Passado**: Vendas do mês anterior

### 5. Widgets e Estatísticas

#### Widget de Estatísticas Gerais:
- Total de vendas acumulado
- Vendas do mês atual (com comparação)
- Valor pendente de pagamento
- Número total de transações
- Quantidade total de itens vendidos
- Melhor cliente (maior valor)

#### Gráfico de Vendas por Mês:
- Gráfico de linha dos últimos 12 meses
- Valores em reais formatados

#### Gráfico de Vendas por Produto:
- Gráfico de rosca (doughnut) com top 10 produtos
- Cores diferenciadas para cada produto

#### Gráfico de Vendas por Departamento:
- Gráfico de barras agrupado por departamento
- Baseado no departamento do cliente

#### Tabela Top 10 Clientes:
- Lista dos melhores clientes
- Número de pedidos por cliente
- Total de vendas por cliente
- Ticket médio calculado

### 6. Recursos Adicionais
- **Resumo no Cabeçalho**: Total geral, quantidade e valores pendentes
- **Totalizadores**: Soma automática dos valores na tabela
- **Paginação**: Opções de 10, 25, 50 ou 100 registros por página
- **Ordenação**: Todas as colunas são ordenáveis
- **Busca**: Busca por nome do cliente e produto
- **Exportação**: Botão preparado para futuras implementações

## Arquivos Criados

### Recursos Principais:
- `app/Filament/Resources/SalesReportResource.php`
- `app/Filament/Resources/SalesReportResource/Pages/ListSalesReports.php`

### Widgets:
- `app/Filament/Resources/SalesReportResource/Widgets/SalesStatsWidget.php`
- `app/Filament/Resources/SalesReportResource/Widgets/SalesChartWidget.php`
- `app/Filament/Resources/SalesReportResource/Widgets/ProductSalesWidget.php`
- `app/Filament/Resources/SalesReportResource/Widgets/DepartmentSalesWidget.php`
- `app/Filament/Resources/SalesReportResource/Widgets/TopCustomersWidget.php`

## Como Usar

1. **Acesse o Admin Panel**: `/admin`
2. **Navegue para Relatórios**: Menu lateral "Relatórios"
3. **Clique em "Relatório de Vendas"**
4. **Use os filtros** conforme necessário:
   - Selecione cliente específico
   - Escolha produto
   - Defina período (mês/ano ou intervalo)
   - Filtre por status de pagamento
5. **Navegue pelas abas** para visualizações rápidas
6. **Analise os gráficos e estatísticas** na parte inferior

## Benefícios

- **Visão Completa**: Todos os dados de vendas em um local
- **Análise Temporal**: Acompanhamento de tendências mensais
- **Identificação de Padrões**: Melhores clientes e produtos
- **Controle Financeiro**: Valores pagos vs pendentes
- **Interface Intuitiva**: Fácil navegação e filtros
- **Responsivo**: Funciona em desktop e mobile

## Próximas Melhorias Sugeridas

1. **Exportação para Excel/PDF**
2. **Relatórios por período personalizado**
3. **Alertas para vendas em atraso**
4. **Comparativos entre períodos**
5. **Metas de vendas e acompanhamento**
6. **Relatórios por vendedor (se aplicável)**

## Tecnologias Utilizadas

- **Laravel**: Framework PHP
- **Filament**: Admin Panel
- **Chart.js**: Gráficos interativos
- **Tailwind CSS**: Estilização
- **MySQL**: Banco de dados
