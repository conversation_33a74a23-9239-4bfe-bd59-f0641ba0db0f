<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ChargeConfigResource\Pages;
use App\Filament\Resources\ChargeConfigResource\RelationManagers;
use App\Models\ChargeConfig;
use Filament\Forms;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChargeConfigResource extends Resource
{
    protected static ?string $label = 'Configuração de Cobrança';

    protected static ?string $pluralLabel = 'Configurações de Cobrança';

    protected static ?string $model = ChargeConfig::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('pix_name')
                    ->label('Nome')
                    ->placeholder('Nome que aparecerá ao inserir a chave pix')
                    ->required()
                    ->maxLength(191),

                Forms\Components\TextInput::make('pix_key')
                    ->label('Chave Pix')
                    ->required()
                    ->maxLength(191),

                Forms\Components\Select::make('pix_type')
                    ->label('Tipo da chave')
                    ->options([
                        'cpf' => 'CPF',
                        'email' => 'E-mail',
                        'phone' => 'Telefone',
                        'random' => 'Chave aleatória',
                    ])
                    ->searchable()
                    ->required(),

                Forms\Components\TextInput::make('pix_bank')
                    ->label('Banco da chave')
                    ->required()
                    ->maxLength(191),

                Forms\Components\RichEditor::make('whatsapp_template')
                    ->label('Template de mensagem')
                    ->required()
                    ->columnSpanFull(),

                ViewField::make('helper_text')
                    ->view('filament.forms.components.charge-config-helper-text')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('pix_name')
                    ->label('Nome')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('pix_key')
                    ->label('Chave')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('pix_type')
                    ->label('Tipo')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('pix_bank')
                    ->label('Banco')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListChargeConfigs::route('/'),
            // 'create' => Pages\CreateChargeConfig::route('/create'),
            'edit' => Pages\EditChargeConfig::route('/{record}/edit'),
        ];
    }
}
