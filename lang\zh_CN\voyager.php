<?php

return [
    'date' => [
        'last_week' => '上周',
        'last_year' => '去年',
        'this_week' => '本周',
        'this_year' => '今年',
    ],
    'generic' => [
        'action'                 => '操作',
        'actions'                => '操作',
        'add'                    => '添加',
        'add_folder'             => '添加文件夹',
        'add_new'                => '添加',
        'all_done'               => '已全部完成',
        'are_you_sure'           => '您确定吗？',
        'are_you_sure_delete'    => '你确定要删除吗',
        'auto_increment'         => '自增',
        'browse'                 => '浏览',
        'builder'                => '构建器',
        'bulk_delete'            => '删除选中',
        'bulk_delete_confirm'    => '是的, 删除这些',
        'bulk_delete_nothing'    => '没有选择要删除的内容',
        'cancel'                 => '取消',
        'choose_type'            => '选择类型',
        'click_here'             => '点击这里',
        'close'                  => '关闭',
        'compass'                => '指南针',
        'created_at'             => 'created_at',
        'custom'                 => '自定义',
        'dashboard'              => '控制面板',
        'database'               => '数据库',
        'default'                => '默认',
        'delete'                 => '删除',
        'delete_confirm'         => '是的,删除它!',
        'delete_question'        => '您确定要删除它吗？',
        'delete_this_confirm'    => '是的，我要删除！',
        'deselect_all'           => '反选全部',
        'download'               => '下载',
        'edit'                   => '编辑',
        'email'                  => '电子邮件',
        'error_deleting'         => '抱歉，在删除过程中出现了问题',
        'exception'              => '异常',
        'featured'               => '特色',
        'field_does_not_exist'   => '字段不存在',
        'how_to_use'             => '如何使用',
        'index'                  => 'INDEX',
        'internal_error'         => '内部错误',
        'items'                  => '项目',
        'keep_sidebar_open'      => '保持边栏处在打开状态',
        'key'                    => '键',
        'last_modified'          => 'last_modified',
        'length'                 => '长度',
        'login'                  => '登录',
        'media'                  => '媒体',
        'menu_builder'           => '菜单生成器',
        'move'                   => '移动',
        'name'                   => '命名',
        'new'                    => '新',
        'no'                     => '没有',
        'no_thanks'              => '不，谢谢',
        'not_null'               => '非空',
        'options'                => '选项',
        'password'               => '密码',
        'permissions'            => '权限',
        'profile'                => '个人资料',
        'public_url'             => '公开 URL',
        'read'                   => '读',
        'rename'                 => '重命名',
        'required'               => '必须',
        'return_to_list'         => '返回列表',
        'route'                  => '路由',
        'save'                   => '保存',
        'search'                 => '搜索',
        'select_all'             => '选择全部',
        'settings'               => '设置',
        'showing_entries'        => '展示从 :from 到 :to 项结果，共 :all 项|展示从 :from 到 :to 项结果，共 :all 项',
        'submit'                 => '发布',
        'successfully_added_new' => '添加成功',
        'successfully_deleted'   => '删除成功',
        'successfully_updated'   => '更新成功',
        'timestamp'              => '时间戳',
        'title'                  => '标题',
        'type'                   => '类型',
        'unsigned'               => 'Unsigned',
        'unstick_sidebar'        => '取消固定侧边栏',
        'update'                 => '更新',
        'update_failed'          => '更新失败',
        'upload'                 => '上传',
        'url'                    => '网址',
        'view'                   => '视图',
        'viewing'                => '查看',
        'yes'                    => '好的',
        'yes_please'             => '好的，就这样做',
    ],
    'login' => [
        'loggingin'    => '正在登录',
        'signin_below' => '在下方登录：',
        'welcome'      => '欢迎使用 Voyager - 不可错过的 Laravel 后台管理框架',
    ],
    'profile' => [
        'avatar'        => '头像',
        'edit'          => '更改个人资料',
        'edit_user'     => '编辑用户',
        'password'      => '密码',
        'password_hint' => '留空为不修改密码',
        'role'          => '权限',
        'user_role'     => '用户权限',
    ],
    'settings' => [
        'usage_help'           => '通过调用，您可以在站点的任何地方获得每个设置的值',
        'save'                 => '保存设置',
        'new'                  => '新设置',
        'help_name'            => '设置名称 例如：管理标题',
        'help_key'             => '设置键（key） 例如：admin_title',
        'help_option'          => '(可选。仅适用于下拉框或单选按钮之类的某些类型)',
        'add_new'              => '添加新设置',
        'delete_question'      => '您确定要删除 :setting 设置吗?',
        'delete_confirm'       => '是的，删除此设置',
        'successfully_created' => '成功创建了设置',
        'successfully_saved'   => '成功保存设置',
        'successfully_deleted' => '成功删除设置',
        'already_at_top'       => '已经在顶部了',
        'already_at_bottom'    => '已经在底部了',
        'key_already_exists'   => '键 :key 已存在',
        'moved_order_up'       => '已将 :name 设置抬升',
        'moved_order_down'     => '已将 :name 设置下沉',
        'successfully_removed' => '成功移除 :name 的值',
        'group_general'        => '概览',
        'group_admin'          => '管理',
        'group_site'           => '站点',
        'group'                => '组',
        'help_group'           => '这个设置被分配给',
    ],
    'media' => [
        'add_new_folder'         => '添加新文件夹',
        'audio_support'          => '您的浏览器不支持音频元素。',
        'create_new_folder'      => '创建新文件夹',
        'delete_folder_question' => '此操作将连同其内的所有文件和文件夹一并删除',
        'destination_folder'     => '目标文件夹',
        'drag_drop_info'         => '拖放文件或点击下面的上传',
        'error_already_exists'   => '对不起，相同名称的文件 / 文件夹已存在。',
        'error_creating_dir'     => '对不起，创建目录似乎出了问题，请检查您的权限',
        'error_deleting_file'    => '抱歉，在删除此文件时出现了错误，请检查您的权限',
        'error_deleting_folder'  => '对不起，在删除此文件夹时出现了错误，请检查您的权限',
        'error_may_exist'        => '可能已存在同名的文件或文件夹。请选择另一个名称或删除现有文件。',
        'error_moving'           => '对不起，在移动文件 / 文件夹时出现了问题，请确保您有正确的权限。',
        'error_uploading'        => '上传失败：发生未知错误！',
        'folder_exists_already'  => '对不起，文件夹已经存在，如果您想重新创建，请删除该文件夹',
        'image_does_not_exist'   => '图片不存在',
        'image_removed'          => '图片已删除',
        'library'                => '媒体库',
        'loading'                => '加载你的媒体文件',
        'move_file_folder'       => '移动文件或文件夹',
        'new_file_folder'        => '新文件 / 文件夹的名字',
        'new_folder_name'        => '新文件夹名称',
        'no_files_here'          => '没有文件。',
        'no_files_in_folder'     => '这个文件夹中没有文件。',
        'nothing_selected'       => '没有选择文件或文件夹',
        'rename_file_folder'     => '重命名文件或文件夹',
        'success_uploaded_file'  => '成功上传新文件!',
        'success_uploading'      => '图片上传成功!',
        'uploading_wrong_type'   => '上传失败：不受支持的文件格式，或是它文件过大而无法上传!',
        'video_support'          => '您的浏览器不支持视频标签。',
    ],
    'menu_builder' => [
        'color'                => 'RGB或hex中的颜色(可选)',
        'color_ph'             => '颜色 (例如：#ffffff 或 rgb(255, 255, 255)',
        'create_new_item'      => '创建一个新的菜单项',
        'delete_item_confirm'  => '是的，删除这个菜单项',
        'delete_item_question' => '您确定要删除这个菜单项吗？',
        'drag_drop_info'       => '拖放下面的菜单项重新排列。',
        'dynamic_route'        => '动态路由',
        'edit_item'            => '编辑菜单项',
        'icon_class'           => '菜单项的字体图标类（使用',
        'icon_class2'          => 'Voyager 图标库</a>）',
        'icon_class_ph'        => 'Icon Class（可选）',
        'item_route'           => '菜单项的路由',
        'item_title'           => '菜单项的标题',
        'link_type'            => '链接类型',
        'new_menu_item'        => '新菜单项',
        'open_in'              => '打开',
        'open_new'             => '新标签页 / 窗口打开',
        'open_same'            => '在相同标签 / 窗口打开',
        'route_parameter'      => '路由参数（如果存在）',
        'static_url'           => '静态 URL',
        'successfully_created' => '成功创建新菜单项。',
        'successfully_deleted' => '成功删除菜单项。',
        'successfully_updated' => '成功更新菜单项。',
        'updated_order'        => '成功更新菜单顺序。',
        'url'                  => '菜单项的 URL',
        'usage_hint'           => 'You can output a menu anywhere on your site by calling|You can output '.
                                   'this menu anywhere on your site by calling',
    ],
    'post' => [
        'category'         => '分类目录',
        'content'          => '文章内容',
        'details'          => '文章详细信息',
        'excerpt'          => '文章摘要 <small>对该篇文章的简短描述</small>',
        'image'            => '文章图片',
        'meta_description' => 'Meta Description',
        'meta_keywords'    => 'Meta Keywords',
        'new'              => '创建新文章',
        'seo_content'      => 'SEO Content',
        'seo_title'        => 'Seo Title',
        'slug'             => 'URL Slug',
        'status'           => '发布状态',
        'status_draft'     => '草稿',
        'status_pending'   => '待审核',
        'status_published' => '已发布',
        'title'            => '文章标题',
        'title_sub'        => '该篇文章的标题',
        'update'           => '更新文章',
    ],
    'database' => [
        'add_bread'                 => '添加 BREAD 至该表',
        'add_new_column'            => '添加新列',
        'add_softdeletes'           => '添加 Soft Deletes',
        'add_timestamps'            => '添加时间戳',
        'already_exists'            => '已存在',
        'already_exists_table'      => '表 :table 已经存在',
        'bread_crud_actions'        => 'BREAD / CRUD 操作',
        'bread_info'                => 'BREAD 信息',
        'column'                    => '列',
        'composite_warning'         => '警告：此列是复合索引的一部分',
        'controller_name'           => 'Controller 名称',
        'controller_name_hint'      => '例如：PageController，如果留空将使用自带的 BREAD Controller',
        'create_bread_for_table'    => '为表 :table 创建 BREAD',
        'create_migration'          => '为该表创建迁移？',
        'create_model_table'        => '为该表创建模型（Model）？',
        'create_new_table'          => '创建新表',
        'create_your_new_table'     => '创建新表',
        'default'                   => '默认',
        'delete_bread'              => '删除 BREAD',
        'delete_bread_before_table' => '请务必在删除表前先删除该表的 BREAD。',
        'delete_table_bread_conf'   => '是的，删除该 BREAD',
        'delete_table_bread_quest'  => '你确定要删除 :table 表的 BREAD吗？',
        'delete_table_confirm'      => '是的，删除该表',
        'delete_table_question'     => '您确定要删除 :table 表吗?',
        'description'               => '描述',
        'display_name'              => '显示名称',
        'display_name_plural'       => '显示名称（复数）',
        'display_name_singular'     => '显示名称（单数）',
        'edit_bread'                => '编辑 BREAD',
        'edit_bread_for_table'      => '编辑 BREAD :table',
        'edit_rows'                 => '在下方编辑 :table 行',
        'edit_table'                => '在下方编辑 :table 表',
        'edit_table_not_exist'      => '您想要编辑的表不存在',
        'error_creating_bread'      => '很抱歉，在创建 BREAD 时出现了问题',
        'error_removing_bread'      => '很抱歉，在删除 BREAD 时出现了问题',
        'error_updating_bread'      => '很抱歉，在更新 BREAD 时出现了问题',
        'extra'                     => '额外',
        'field'                     => '字段',
        'field_safe_failed'         => '未能保存字段 :field，正在回滚操作！',
        'generate_permissions'      => '权限生成',
        'icon_class'                => '用于该表的图标',
        'icon_hint'                 => '使用图标（可选）',
        'icon_hint2'                => 'Voyager 字体类',
        'index'                     => 'INDEX',
        'input_type'                => '输入类型',
        'key'                       => '键',
        'model_class'               => '模型类名称',
        'model_name'                => '模型名称',
        'model_name_ph'             => '如果左侧留空，将尝试使用表名',
        'name_warning'              => '请在添加索引之前给列命名',
        'no_composites_warning'     => '此表有复合索引。请注意，他们目前不受支持。在尝试添加 / 删除索引时要小心。',
        'null'                      => '空',
        'optional_details'          => '可选细项',
        'primary'                   => '主',
        'server_pagination'         => '服务器端分页',
        'success_create_table'      => '成功创建了:table 表',
        'success_created_bread'     => '成功创建 BREAD',
        'success_delete_table'      => '成功删除表 :table',
        'success_remove_bread'      => '成功地从 :datatype 中移除 BREAD',
        'success_update_bread'      => '成功更新 :datatype BREAD',
        'success_update_table'      => '成功更新 :table 表',
        'table_actions'             => '表操作',
        'table_columns'             => '表列',
        'table_has_index'           => '该表已经有一个主索引。',
        'table_name'                => '表名',
        'table_no_columns'          => '该表没有列…',
        'type'                      => '类型',
        'type_not_supported'        => '不支持这种类型',
        'unique'                    => '唯一',
        'unknown_type'              => '未知类型',
        'update_table'              => '更新表',
        'url_slug'                  => 'URL Slug（必须是唯一的）',
        'url_slug_ph'               => 'URL Slug（例如文章）',
        'visibility'                => '可见性',
    ],
    'dimmer' => [
        'page'           => '页面|页面',
        'page_link_text' => '查看所有页面',
        'page_text'      => '您有 :count :string 在数据库中。点击下面的按钮查看所有页面。',
        'post'           => '文章|文章',
        'post_link_text' => '查看所有的帖子',
        'post_text'      => '您有 :count :string 在数据库中。点击下面的按钮查看所有文章。',
        'user'           => '用户|用户',
        'user_link_text' => '查看所有用户',
        'user_text'      => '您有 :count :string 在数据库中。点击下面的按钮查看所有用户。',
    ],
    'form' => [
        'field_password_keep'          => '留空以保持不变',
        'field_select_dd_relationship' => '确保在 :class 类的 :method 方法中设置适当的关系。',
        'type_checkbox'                => '复选框',
        'type_codeeditor'              => '代码编辑器',
        'type_file'                    => '文件',
        'type_image'                   => '图像',
        'type_radiobutton'             => '单选按钮',
        'type_richtextbox'             => '富文本框',
        'type_selectdropdown'          => '选择下拉',
        'type_textarea'                => '文本区域',
        'type_textbox'                 => '文本框',
    ],
    // DataTable translations from: https://github.com/DataTables/Plugins/tree/master/i18n
'datatable' => [
        'sEmptyTable'     => '处理中...',
        'sInfo'           => '显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项',
        'sInfoEmpty'      => '显示第 0 至 0 项结果，共 0 项',
        'sInfoFiltered'   => '(由 _MAX_ 项结果过滤)',
        'sInfoPostFix'    => '',
        'sInfoThousands'  => ',',
        'sLengthMenu'     => '显示 _MENU_ 项结果',
        'sLoadingRecords' => '载入中...',
        'sProcessing'     => '处理中...',
        'sSearch'         => '搜索：',
        'sZeroRecords'    => '没有匹配结果',
        'oPaginate'       => [
            'sFirst'    => '首页',
            'sLast'     => '末页',
            'sNext'     => '下页',
            'sPrevious' => '上页',
        ],
        'oAria' => [
            'sSortAscending'  => ': 以升序排列此列',
            'sSortDescending' => ': 以降序排列此列',
        ],
    ],
    'theme' => [
        'footer_copyright'  => 'Made with <i class="voyager-heart"></i> by',
        'footer_copyright2' => 'Made with rum and even more rum',
    ],
    'json' => [
        'invalid'           => '无效的 Json',
        'invalid_message'   => '看起来您引入了一些无效的 JSON',
        'valid'             => '有效的 Json',
        'validation_errors' => '验证错误',
    ],
    'analytics' => [
        'by_pageview'  => 'By pageview',
        'by_sessions'  => 'By sessions',
        'by_users'     => 'By users',
        'no_client_id' => 'To view analytics you\'ll need to get a google analytics client id and '.
                                     'add it to your settings for the key <code>google_analytics_client_id'.
                                     '</code>. Get your key in your Google developer console:',
        'set_view'               => 'Select a View',
        'this_vs_last_week'      => 'This Week vs Last Week',
        'this_vs_last_year'      => 'This Year vs Last Year',
        'top_browsers'           => 'Top Browsers',
        'top_countries'          => 'Top Countries',
        'various_visualizations' => 'Various visualizations',
    ],
    'error' => [
        'symlink_created_text'   => '我们刚刚为您创建了缺失的软连接。',
        'symlink_created_title'  => '丢失的存储软连接已被重新创建',
        'symlink_failed_text'    => '我们未能为您的应用程序生成缺失的软连接，似乎您的主机提供商不支持它。',
        'symlink_failed_title'   => '无法创建丢失的存储软连接',
        'symlink_missing_button' => '修复',
        'symlink_missing_text'   => '我们找不到一个存储软连接，这可能会导致'.
                                    '从浏览器加载媒体文件的问题。',
        'symlink_missing_title' => '缺失的存储软连接',
    ],
];
