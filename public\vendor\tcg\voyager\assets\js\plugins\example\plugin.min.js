tinymce.PluginManager.add("example",function(e,t){e.add<PERSON>utton("example",{text:"My button",icon:!1,onclick:function(){e.windowManager.open({title:"Example plugin",body:[{type:"textbox",name:"title",label:"Title"}],onsubmit:function(t){e.insertContent("Title: "+t.data.title)}})}}),e.addMenuItem("example",{text:"Example plugin",context:"tools",onclick:function(){e.windowManager.open({title:"TinyMCE site",url:t+"/dialog.html",width:600,height:400,buttons:[{text:"Insert",onclick:function(){var t=e.windowManager.getWindows()[0];e.insertContent(t.getContentWindow().document.getElementById("content").value),t.close()}},{text:"Close",onclick:"close"}]})}})});