<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class PendingSalesWidget extends BaseWidget
{
    protected static ?int $sort = 30;

    protected function getStats(): array
    {
        // Mês anterior
        $now = Carbon::now();
        $previousMonth = $now->copy()->startOfMonth()->subMonth();
        $endOfPreviousMonth = $previousMonth->copy()->endOfMonth();

        // Vendas pendentes do mês anterior
        $pendingSales = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])
            ->where('paid', false)
            ->get();

        $totalPendingQuantity = $pendingSales->sum('quantity');
        $totalPendingValue = $pendingSales->sum('total');
        $totalPendingCount = $pendingSales->count();

        // Total de vendas do mês anterior para calcular percentual
        $totalSales = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])->get();
        $totalSalesValue = $totalSales->sum('total');
        $totalSalesCount = $totalSales->count();

        // Calcular percentual de vendas pendentes
        $pendingPercentage = $totalSalesValue > 0
            ? ($totalPendingValue / $totalSalesValue) * 100
            : 0;

        $countPercentage = $totalSalesCount > 0
            ? ($totalPendingCount / $totalSalesCount) * 100
            : 0;

        $monthName = $previousMonth->locale('pt_BR')->isoFormat('MMMM YYYY');

        return [
            Stat::make('Vendas Pendentes - ' . $monthName, $totalPendingCount . ' vendas')
                ->description(number_format($countPercentage, 1) . '% do total de vendas do mês')
                ->descriptionIcon('heroicon-m-exclamation-triangle')
                ->color('warning')
                ->chart([2, 4, 3, 6, 5, 7, 4]),

            Stat::make('Valor Pendente - ' . $monthName, 'R$ ' . number_format($totalPendingValue, 2, ',', '.'))
                ->description(number_format($pendingPercentage, 1) . '% do valor total do mês')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('danger')
                ->chart([2, 4, 3, 6, 5, 7, 4]),

            Stat::make('Quantidade Pendente - ' . $monthName, number_format($totalPendingQuantity, 0, ',', '.') . ' itens')
                ->description('Itens aguardando pagamento')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning')
                ->chart([1, 3, 2, 5, 4, 6, 3]),
        ];
    }
}
