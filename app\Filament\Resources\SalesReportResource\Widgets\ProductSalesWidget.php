<?php

namespace App\Filament\Resources\SalesReportResource\Widgets;

use App\Models\Sale;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class ProductSalesWidget extends ChartWidget
{
    protected static ?string $heading = 'Vendas por Produto (Top 10)';
    protected static ?int $sort = 3;

    protected function getData(): array
    {
        // Pegar os 10 produtos mais vendidos
        $productSales = Sale::select('product_id', DB::raw('SUM(total) as total_sales'))
            ->with('product')
            ->groupBy('product_id')
            ->orderBy('total_sales', 'desc')
            ->limit(10)
            ->get();

        $labels = [];
        $data = [];
        $colors = [
            'rgba(255, 99, 132, 0.8)',
            'rgba(54, 162, 235, 0.8)',
            'rgba(255, 205, 86, 0.8)',
            'rgba(75, 192, 192, 0.8)',
            'rgba(153, 102, 255, 0.8)',
            'rgba(255, 159, 64, 0.8)',
            'rgba(199, 199, 199, 0.8)',
            'rgba(83, 102, 255, 0.8)',
            'rgba(255, 99, 255, 0.8)',
            'rgba(99, 255, 132, 0.8)',
        ];

        foreach ($productSales as $sale) {
            $labels[] = $sale->product->name ?? 'Produto não encontrado';
            $data[] = (float) $sale->total_sales;
        }

        return [
            'datasets' => [
                [
                    'label' => 'Vendas (R$)',
                    'data' => $data,
                    'backgroundColor' => array_slice($colors, 0, count($data)),
                    'borderWidth' => 1,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            return context.label + ": R$ " + context.parsed.toLocaleString("pt-BR", {minimumFractionDigits: 2});
                        }',
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
