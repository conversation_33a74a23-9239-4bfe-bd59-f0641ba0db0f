!function(e,t){"use strict";function n(e,t){for(var n,r=[],a=0;a<e.length;++a){if(n=o[e[a]]||i(e[a]),!n)throw"module definition dependecy not found: "+e[a];r.push(n)}t.apply(null,r)}function r(e,r,i){if("string"!=typeof e)throw"invalid module definition, module id must be defined and be a string";if(r===t)throw"invalid module definition, dependencies must be specified";if(i===t)throw"invalid module definition, definition function must be specified";n(r,function(){o[e]=i.apply(null,arguments)})}function i(t){for(var n=e,r=t.split(/[.\/]/),i=0;i<r.length;++i){if(!n[r[i]])return;n=n[r[i]]}return n}var o={};r("tinymce/tableplugin/Utils",["tinymce/Env"],function(e){function t(t){(!e.ie||e.ie>9)&&(t.hasChildNodes()||(t.innerHTML='<br data-mce-bogus="1" />'))}var n=function(e){return function(t,n){t&&(n=parseInt(n,10),1===n||0===n?t.removeAttribute(e,1):t.setAttribute(e,n,1))}},r=function(e){return function(t){return parseInt(t.getAttribute(e)||1,10)}};return{setColSpan:n("colSpan"),setRowSpan:n("rowspan"),getColSpan:r("colSpan"),getRowSpan:r("rowSpan"),setSpanVal:function(e,t,r){n(t)(e,r)},getSpanVal:function(e,t){return r(t)(e)},paddCell:t}}),r("tinymce/tableplugin/SplitCols",["tinymce/util/Tools","tinymce/tableplugin/Utils"],function(e,t){var n=function(e,t,n){return e[n]?e[n][t]:null},r=function(e,t,r){var i=n(e,t,r);return i?i.elm:null},i=function(e,t,i,o){var a,s,l=0,c=r(e,t,i);for(a=i;(o>0?a<e.length:a>=0)&&(s=n(e,t,a),c===s.elm);a+=o)l++;return l},o=function(e,t,n){for(var r,i=e[n],o=t;o<i.length;o++)if(r=i[o],r.real)return r.elm;return null},a=function(e,n){for(var r,o=[],a=e[n],s=0;s<a.length;s++)r=a[s],o.push({elm:r.elm,above:i(e,s,n,-1)-1,below:i(e,s,n,1)-1}),s+=t.getColSpan(r.elm)-1;return o},s=function(e,n){var r=e.elm.ownerDocument,i=r.createElement("td");return t.setColSpan(i,t.getColSpan(e.elm)),t.setRowSpan(i,n),t.paddCell(i),i},l=function(e,t,n,r){var i=o(e,n+1,r);i?i.parentNode.insertBefore(t,i):(i=o(e,0,r),i.parentNode.appendChild(t))},c=function(e,n,r,i){if(0!==n.above){t.setRowSpan(n.elm,n.above);var o=s(n,n.below+1);return l(e,o,r,i),o}return null},u=function(e,n,r,i){if(0!==n.below){t.setRowSpan(n.elm,n.above+1);var o=s(n,n.below);return l(e,o,r,i+1),o}return null},d=function(t,n,i,o){var s=a(t,i),l=r(t,n,i).parentNode,d=[];return e.each(s,function(e,n){var r=o?c(t,e,n,i):u(t,e,n,i);null!==r&&d.push(d)}),{cells:d,row:l}};return{splitAt:d}}),r("tinymce/tableplugin/TableGrid",["tinymce/util/Tools","tinymce/Env","tinymce/tableplugin/Utils","tinymce/tableplugin/SplitCols"],function(e,n,r,i){var o=e.each,a=r.getSpanVal,s=r.setSpanVal;return function(l,c,u){function d(){l.$("td[data-mce-selected],th[data-mce-selected]").removeAttr("data-mce-selected")}function f(e){return e===l.getBody()}function p(t,n){return t?(n=e.map(n.split(","),function(e){return e.toLowerCase()}),e.grep(t.childNodes,function(t){return e.inArray(n,t.nodeName.toLowerCase())!==-1})):[]}function m(){var e=0;Q=[],Z=0,o(["thead","tbody","tfoot"],function(t){var n=p(c,t)[0],r=p(n,"tr");o(r,function(n,r){r+=e,o(p(n,"td,th"),function(e,n){var i,o,s,l;if(Q[r])for(;Q[r][n];)n++;for(s=a(e,"rowspan"),l=a(e,"colspan"),o=r;o<r+s;o++)for(Q[o]||(Q[o]=[]),i=n;i<n+l;i++)Q[o][i]={part:t,real:o==r&&i==n,elm:e,rowspan:s,colspan:l};Z=Math.max(Z,n+1)})}),e+=r.length})}function h(e){return l.fire("newrow",{node:e}),e}function g(e){return l.fire("newcell",{node:e}),e}function v(e,t){return e=e.cloneNode(t),e.removeAttribute("id"),e}function b(e,t){var n;if(n=Q[t])return n[e]}function y(e,t){return e[t]?e[t]:null}function C(e,t){for(var n=[],r=0;r<e.length;r++)n.push(b(t,r));return n}function x(e){return e&&(!!re.getAttrib(e.elm,"data-mce-selected")||e==u)}function w(){var e=[];return o(c.rows,function(t){o(t.cells,function(n){if(re.getAttrib(n,"data-mce-selected")||u&&n==u.elm)return e.push(t),!1})}),e}function N(){var e=0;return o(Q,function(t){if(o(t,function(t){x(t)&&e++}),e)return!1}),e}function E(){var e=re.createRng();f(c)||(e.setStartAfter(c),e.setEndAfter(c),ne.setRng(e),re.remove(c))}function S(t){var i,a={};return l.settings.table_clone_elements!==!1&&(a=e.makeMap((l.settings.table_clone_elements||"strong em b i span font h1 h2 h3 h4 h5 h6 p div").toUpperCase(),/[ ,]/)),e.walk(t,function(e){var r;if(3==e.nodeType)return o(re.getParents(e.parentNode,null,t).reverse(),function(e){a[e.nodeName]&&(e=v(e,!1),i?r&&r.appendChild(e):i=r=e,r=e)}),r&&(r.innerHTML=n.ie&&n.ie<10?"&nbsp;":'<br data-mce-bogus="1" />'),!1},"childNodes"),t=v(t,!1),g(t),s(t,"rowSpan",1),s(t,"colSpan",1),i?t.appendChild(i):r.paddCell(t),t}function _(){var e,t=re.createRng();return o(re.select("tr",c),function(e){0===e.cells.length&&re.remove(e)}),0===re.select("tr",c).length?(t.setStartBefore(c),t.setEndBefore(c),ne.setRng(t),void re.remove(c)):(o(re.select("thead,tbody,tfoot",c),function(e){0===e.rows.length&&re.remove(e)}),m(),void(ee&&(e=Q[Math.min(Q.length-1,ee.y)],e&&(ne.select(e[Math.min(e.length-1,ee.x)].elm,!0),ne.collapse(!0)))))}function k(e,t,n,r){var i,o,a,s,l;for(i=Q[t][e].elm.parentNode,a=1;a<=n;a++)if(i=re.getNext(i,"tr")){for(o=e;o>=0;o--)if(l=Q[t+a][o].elm,l.parentNode==i){for(s=1;s<=r;s++)re.insertAfter(S(l),l);break}if(o==-1)for(s=1;s<=r;s++)i.insertBefore(S(i.cells[0]),i.cells[0])}}function T(){o(Q,function(e,t){o(e,function(e,n){var r,i,o;if(x(e)&&(e=e.elm,r=a(e,"colspan"),i=a(e,"rowspan"),r>1||i>1)){for(s(e,"rowSpan",1),s(e,"colSpan",1),o=0;o<r-1;o++)re.insertAfter(S(e),e);k(n,t,i-1,r)}})})}function R(e,t,n){for(var r=[],i=0;i<e.length;i++)(i<t||i>n)&&r.push(e[i]);return r}function A(t){return e.grep(t,function(e){return e.real===!1})}function B(e){for(var t=[],n=0;n<e.length;n++){var r=e[n].elm;t[t.length-1]!==r&&t.push(r)}return t}function P(t,n,i,o,a){var s=0;if(a-i<1)return 0;for(var l=i+1;l<=a;l++){var c=R(y(t,l),n,o),u=A(c);c.length===u.length&&(e.each(B(u),function(e){r.setRowSpan(e,r.getRowSpan(e)-1)}),s++)}return s}function D(t,n,i,o,a){var s=0;if(o-n<1)return 0;for(var l=n+1;l<=o;l++){var c=R(C(t,l),i,a),u=A(c);c.length===u.length&&(e.each(B(u),function(e){r.setColSpan(e,r.getColSpan(e)-1)}),s++)}return s}function M(t,n,r){var i,a,l,c,u,d,f,p,h,g,v,y,C;if(t?(i=q(t),a=i.x,l=i.y,c=a+(n-1),u=l+(r-1)):(ee=te=null,o(Q,function(e,t){o(e,function(e,n){x(e)&&(ee||(ee={x:n,y:t}),te={x:n,y:t})})}),ee&&(a=ee.x,l=ee.y,c=te.x,u=te.y)),p=b(a,l),h=b(c,u),p&&h&&p.part==h.part){T(),m(),y=P(Q,a,l,c,u),C=D(Q,a,l,c,u),p=b(a,l).elm;var w=c-a-C+1,N=u-l-y+1;for(w===Z&&N===Q.length&&(w=1,N=1),w===Z&&N>1&&(N=1),s(p,"colSpan",w),s(p,"rowSpan",N),f=l;f<=u;f++)for(d=a;d<=c;d++)Q[f]&&Q[f][d]&&(t=Q[f][d].elm,t!=p&&(g=e.grep(t.childNodes),o(g,function(e){p.appendChild(e)}),g.length&&(g=e.grep(p.childNodes),v=0,o(g,function(e){"BR"==e.nodeName&&v++<g.length-1&&p.removeChild(e)})),re.remove(t)));_()}}function L(e){var n,r,i,l,c,u,d,f,p,m;if(o(Q,function(r,i){if(o(r,function(t){if(x(t)&&(t=t.elm,c=t.parentNode,u=h(v(c,!1)),n=i,e))return!1}),e)return n===t}),n!==t){for(l=0,m=0;l<Q[0].length;l+=m)if(Q[n][l]&&(r=Q[n][l].elm,m=a(r,"colspan"),r!=i)){if(e){if(n>0&&Q[n-1][l]&&(f=Q[n-1][l].elm,p=a(f,"rowSpan"),p>1)){s(f,"rowSpan",p+1);continue}}else if(p=a(r,"rowspan"),p>1){s(r,"rowSpan",p+1);continue}d=S(r),s(d,"colSpan",r.colSpan),u.appendChild(d),i=r}u.hasChildNodes()&&(e?c.parentNode.insertBefore(u,c):re.insertAfter(u,c))}}function I(e,t){t=t||w().length||1;for(var n=0;n<t;n++)L(e)}function O(e){var n,r;o(Q,function(r){if(o(r,function(t,r){if(x(t)&&(n=r,e))return!1}),e)return n===t}),o(Q,function(t,i){var o,l,c;t[n]&&(o=t[n].elm,o!=r&&(c=a(o,"colspan"),l=a(o,"rowspan"),1==c?e?(o.parentNode.insertBefore(S(o),o),k(n,i,l-1,c)):(re.insertAfter(S(o),o),k(n,i,l-1,c)):s(o,"colSpan",o.colSpan+1),r=o))})}function H(e,t){t=t||N()||1;for(var n=0;n<t;n++)O(e)}function F(t){return e.grep(z(t),x)}function z(e){var t=[];return o(e,function(e){o(e,function(e){t.push(e)})}),t}function W(){var t=[];if(f(c)){if(1==Q[0].length)return;if(F(Q).length==z(Q).length)return}o(Q,function(n){o(n,function(n,r){x(n)&&e.inArray(t,r)===-1&&(o(Q,function(e){var t,n=e[r].elm;t=a(n,"colSpan"),t>1?s(n,"colSpan",t-1):re.remove(n)}),t.push(r))})}),_()}function U(){function e(e){var t,n;o(e.cells,function(e){var n=a(e,"rowSpan");n>1&&(s(e,"rowSpan",n-1),t=q(e),k(t.x,t.y,1,1))}),t=q(e.cells[0]),o(Q[t.y],function(e){var t;e=e.elm,e!=n&&(t=a(e,"rowSpan"),t<=1?re.remove(e):s(e,"rowSpan",t-1),n=e)})}var t;t=w(),f(c)&&t.length==c.rows.length||(o(t.reverse(),function(t){e(t)}),_())}function V(){var e=w();if(!f(c)||e.length!=c.rows.length)return re.remove(e),_(),e}function $(){var e=w();return o(e,function(t,n){e[n]=v(t,!0)}),e}function j(t,n){var r,a,l;t&&(r=i.splitAt(Q,ee.x,ee.y,n),a=r.row,e.each(r.cells,g),l=e.map(t,function(e){return e.cloneNode(!0)}),n||l.reverse(),o(l,function(e){var t,r,i=e.cells.length;for(h(e),t=0;t<i;t++)r=e.cells[t],g(r),s(r,"colSpan",1),s(r,"rowSpan",1);for(t=i;t<Z;t++)e.appendChild(g(S(e.cells[i-1])));for(t=Z;t<i;t++)re.remove(e.cells[t]);n?a.parentNode.insertBefore(e,a):re.insertAfter(e,a)}),d())}function q(e){var t;return o(Q,function(n,r){return o(n,function(n,i){if(n.elm==e)return t={x:i,y:r},!1}),!t}),t}function Y(e){ee=q(e)}function X(){var e,t;return e=t=0,o(Q,function(n,r){o(n,function(n,i){var o,a;x(n)&&(n=Q[r][i],i>e&&(e=i),r>t&&(t=r),n.real&&(o=n.colspan-1,a=n.rowspan-1,o&&i+o>e&&(e=i+o),a&&r+a>t&&(t=r+a)))})}),{x:e,y:t}}function K(e){var t,n,r,i,o,a,s,l,c,u;if(te=q(e),ee&&te){for(t=Math.min(ee.x,te.x),n=Math.min(ee.y,te.y),r=Math.max(ee.x,te.x),i=Math.max(ee.y,te.y),o=r,a=i,u=n;u<=i;u++)for(c=t;c<=r;c++)e=Q[u][c],e.real&&(s=e.colspan-1,l=e.rowspan-1,s&&c+s>o&&(o=c+s),l&&u+l>a&&(a=u+l));for(d(),u=n;u<=a;u++)for(c=t;c<=o;c++)Q[u][c]&&re.setAttrib(Q[u][c].elm,"data-mce-selected","1")}}function G(e,t){var n,r,i;n=q(e),r=n.y*Z+n.x;do{if(r+=t,i=b(r%Z,Math.floor(r/Z)),!i)break;if(i.elm!=e)return ne.select(i.elm,!0),re.isEmpty(i.elm)&&ne.collapse(!0),!0}while(i.elm==e);return!1}function J(t){if(ee){var n=i.splitAt(Q,ee.x,ee.y,t);e.each(n.cells,g)}}var Q,Z,ee,te,ne=l.selection,re=ne.dom;c=c||re.getParent(ne.getStart(!0),"table"),m(),u=u||re.getParent(ne.getStart(!0),"th,td"),u&&(ee=q(u),te=X(),u=b(ee.x,ee.y)),e.extend(this,{deleteTable:E,split:T,merge:M,insertRow:L,insertRows:I,insertCol:O,insertCols:H,splitCols:J,deleteCols:W,deleteRows:U,cutRows:V,copyRows:$,pasteRows:j,getPos:q,setStartCell:Y,setEndCell:K,moveRelIdx:G,refresh:m})}}),r("tinymce/tableplugin/Quirks",["tinymce/util/VK","tinymce/util/Delay","tinymce/Env","tinymce/util/Tools","tinymce/tableplugin/Utils"],function(e,t,n,r,i){var o=r.each,a=i.getSpanVal;return function(s){function l(){function n(n){function r(e,t){var r=e?"previousSibling":"nextSibling",o=s.dom.getParent(t,"tr"),a=o[r];if(a)return v(s,t,a,e),n.preventDefault(),!0;var l=s.dom.getParent(o,"table"),d=o.parentNode,f=d.nodeName.toLowerCase();if("tbody"===f||f===(e?"tfoot":"thead")){var p=i(e,l,d,"tbody");if(null!==p)return c(e,p,t)}return u(e,o,r,l)}function i(e,t,n,r){var i=s.dom.select(">"+r,t),o=i.indexOf(n);if(e&&0===o||!e&&o===i.length-1)return l(e,t);if(o===-1){var a="thead"===n.tagName.toLowerCase()?0:i.length-1;return i[a]}return i[o+(e?-1:1)]}function l(e,t){var n=e?"thead":"tfoot",r=s.dom.select(">"+n,t);return 0!==r.length?r[0]:null}function c(e,t,r){var i=d(t,e);return i&&v(s,r,i,e),n.preventDefault(),!0}function u(e,t,i,o){var a=o[i];if(a)return f(a),!0;var l=s.dom.getParent(o,"td,th");if(l)return r(e,l,n);var c=d(t,!e);return f(c),n.preventDefault(),!1}function d(e,t){var n=e&&e[t?"lastChild":"firstChild"];return n&&"BR"===n.nodeName?s.dom.getParent(n,"td,th"):n}function f(e){s.selection.setCursorLocation(e,0)}function p(){return C==e.UP||C==e.DOWN}function m(e){var t=e.selection.getNode(),n=e.dom.getParent(t,"tr");return null!==n}function h(e){for(var t=0,n=e;n.previousSibling;)n=n.previousSibling,t+=a(n,"colspan");return t}function g(e,t){var n=0,r=0;return o(e.children,function(e,i){if(n+=a(e,"colspan"),r=i,n>t)return!1}),r}function v(e,t,n,r){var i=h(s.dom.getParent(t,"td,th")),o=g(n,i),a=n.childNodes[o],l=d(a,r);f(l||a)}function b(e){var t=s.selection.getNode(),n=s.dom.getParent(t,"td,th"),r=s.dom.getParent(e,"td,th");return n&&n!==r&&y(n,r)}function y(e,t){return s.dom.getParent(e,"TABLE")===s.dom.getParent(t,"TABLE")}var C=n.keyCode;if(p()&&m(s)){var x=s.selection.getNode();t.setEditorTimeout(s,function(){b(x)&&r(!n.shiftKey&&C===e.UP,x,n)},0)}}s.on("KeyDown",function(e){n(e)})}function c(){function e(e,t){var n,r=t.ownerDocument,i=r.createRange();return i.setStartBefore(t),i.setEnd(e.endContainer,e.endOffset),n=r.createElement("body"),n.appendChild(i.cloneContents()),0===n.innerHTML.replace(/<(br|img|object|embed|input|textarea)[^>]*>/gi,"-").replace(/<[^>]+>/g,"").length}s.on("KeyDown",function(t){var n,r,i=s.dom;37!=t.keyCode&&38!=t.keyCode||(n=s.selection.getRng(),r=i.getParent(n.startContainer,"table"),r&&s.getBody().firstChild==r&&e(n,r)&&(n=i.createRng(),n.setStartBefore(r),n.setEndBefore(r),s.selection.setRng(n),t.preventDefault()))})}function u(){s.on("KeyDown SetContent VisualAid",function(){var e;for(e=s.getBody().lastChild;e;e=e.previousSibling)if(3==e.nodeType){if(e.nodeValue.length>0)break}else if(1==e.nodeType&&("BR"==e.tagName||!e.getAttribute("data-mce-bogus")))break;e&&"TABLE"==e.nodeName&&(s.settings.forced_root_block?s.dom.add(s.getBody(),s.settings.forced_root_block,s.settings.forced_root_block_attrs,n.ie&&n.ie<10?"&nbsp;":'<br data-mce-bogus="1" />'):s.dom.add(s.getBody(),"br",{"data-mce-bogus":"1"}))}),s.on("PreProcess",function(e){var t=e.node.lastChild;t&&("BR"==t.nodeName||1==t.childNodes.length&&("BR"==t.firstChild.nodeName||"\xa0"==t.firstChild.nodeValue))&&t.previousSibling&&"TABLE"==t.previousSibling.nodeName&&s.dom.remove(t)})}function d(){function e(e,t,n,r){var i,o,a,s=3,l=e.dom.getParent(t.startContainer,"TABLE");return l&&(i=l.parentNode),o=t.startContainer.nodeType==s&&0===t.startOffset&&0===t.endOffset&&r&&("TR"==n.nodeName||n==i),a=("TD"==n.nodeName||"TH"==n.nodeName)&&!r,o||a}function t(){var t=s.selection.getRng(),n=s.selection.getNode(),r=s.dom.getParent(t.startContainer,"TD,TH");if(e(s,t,n,r)){r||(r=n);for(var i=r.lastChild;i.lastChild;)i=i.lastChild;3==i.nodeType&&(t.setEnd(i,i.data.length),s.selection.setRng(t))}}s.on("KeyDown",function(){t()}),s.on("MouseDown",function(e){2!=e.button&&t()})}function f(){function t(e){s.selection.select(e,!0),s.selection.collapse(!0)}function n(e){s.$(e).empty(),i.paddCell(e)}s.on("keydown",function(i){if((i.keyCode==e.DELETE||i.keyCode==e.BACKSPACE)&&!i.isDefaultPrevented()){var o,a,l,c;if(o=s.dom.getParent(s.selection.getStart(),"table")){if(a=s.dom.select("td,th",o),l=r.grep(a,function(e){return!!s.dom.getAttrib(e,"data-mce-selected")}),0===l.length)return c=s.dom.getParent(s.selection.getStart(),"td,th"),void(s.selection.isCollapsed()&&c&&s.dom.isEmpty(c)&&(i.preventDefault(),n(c),t(c)));i.preventDefault(),s.undoManager.transact(function(){a.length==l.length?s.execCommand("mceTableDelete"):(r.each(l,n),t(l[0]))})}}})}function p(){var t=function(e){return e&&"CAPTION"==e.nodeName&&"TABLE"==e.parentNode.nodeName},r=function(e,t){var n=s.selection.getRng(),r=e.ownerDocument.createTextNode("\xa0");n.startOffset?e.insertBefore(r,e.firstChild):e.appendChild(r),t&&(s.selection.select(r,!0),s.selection.collapse(!0))},i=function(t){return(t.keyCode==e.DELETE||t.keyCode==e.BACKSPACE)&&!t.isDefaultPrevented()},o=function(e){return e.firstChild===e.lastChild&&e.firstChild},a=function(e){return e&&3===e.nodeType},l=function(e){var t=o(e);return a(t)&&1===t.data.length?t.data:null},c=function(e){var t=o(e),n=l(e);return t&&!a(t)||n&&!d(n)},u=function(e){return s.dom.isEmpty(e)||d(l(e))},d=function(e){return"\xa0"===e};s.on("keydown",function(e){if(i(e)){var o=s.dom.getParent(s.selection.getStart(),"caption");t(o)&&(n.ie&&(s.selection.isCollapsed()?c(o)&&r(o):(s.undoManager.transact(function(){s.execCommand("Delete"),u(o)&&r(o,!0)}),e.preventDefault())),u(o)&&e.preventDefault())}})}f(),p(),n.webkit&&(l(),d()),n.gecko&&(c(),u()),n.ie>9&&(c(),u())}}),r("tinymce/tableplugin/CellSelection",["tinymce/tableplugin/TableGrid","tinymce/dom/TreeWalker","tinymce/util/Tools"],function(e,t,n){return function(r,i){function o(e){r.getBody().style.webkitUserSelect="",(e||h)&&(r.$("td[data-mce-selected],th[data-mce-selected]").removeAttr("data-mce-selected"),h=!1)}function a(e,t){return!(!e||!t)&&e===m.getParent(t,"table")}function s(t){var n,o,s=t.target;if(!f&&!p&&s!==d&&(d=s,u&&c)){if(o=m.getParent(s,"td,th"),a(u,o)||(o=m.getParent(u,"td,th")),c===o&&!h)return;if(i(!0),a(u,o)){t.preventDefault(),l||(l=new e(r,u,c),r.getBody().style.webkitUserSelect="none"),l.setEndCell(o),h=!0,n=r.selection.getSel();try{n.removeAllRanges?n.removeAllRanges():n.empty()}catch(e){}}}}var l,c,u,d,f,p,m=r.dom,h=!0,g=function(){c=l=u=d=null,i(!1)};return r.on("SelectionChange",function(e){h&&e.stopImmediatePropagation()},!0),r.on("MouseDown",function(e){2==e.button||f||p||(o(),c=m.getParent(e.target,"td,th"),u=m.getParent(c,"table"))}),r.on("mouseover",s),r.on("remove",function(){m.unbind(r.getDoc(),"mouseover",s),o()}),r.on("MouseUp",function(){function e(e,r){var o=new t(e,e);do{if(3==e.nodeType&&0!==n.trim(e.nodeValue).length)return void(r?i.setStart(e,0):i.setEnd(e,e.nodeValue.length));if("BR"==e.nodeName)return void(r?i.setStartBefore(e):i.setEndBefore(e))}while(e=r?o.next():o.prev())}var i,o,a,s,u,d=r.selection;if(c){if(l&&(r.getBody().style.webkitUserSelect=""),o=m.select("td[data-mce-selected],th[data-mce-selected]"),o.length>0){i=m.createRng(),s=o[0],i.setStartBefore(s),i.setEndAfter(s),e(s,1),a=new t(s,m.getParent(o[0],"table"));do if("TD"==s.nodeName||"TH"==s.nodeName){if(!m.getAttrib(s,"data-mce-selected"))break;u=s}while(s=a.next());e(u),d.setRng(i)}r.nodeChanged(),g()}}),r.on("KeyUp Drop SetContent",function(e){o("setcontent"==e.type),g(),f=!1}),r.on("ObjectResizeStart ObjectResized",function(e){f="objectresized"!=e.type}),r.on("dragstart",function(){p=!0}),r.on("drop dragend",function(){p=!1}),{clear:o}}}),r("tinymce/tableplugin/Dialogs",["tinymce/util/Tools","tinymce/Env"],function(e,t){var n=e.each;return function(r){function i(){var e=r.settings.color_picker_callback;if(e)return function(){var t=this;e.call(r,function(e){t.value(e).fire("change")},t.value())}}function o(e){return{title:"Advanced",type:"form",defaults:{onchange:function(){d(e,this.parents().reverse()[0],"style"==this.name())}},items:[{label:"Style",name:"style",type:"textbox"},{type:"form",padding:0,formItemDefaults:{layout:"grid",alignH:["start","right"]},defaults:{size:7},items:[{label:"Border color",type:"colorbox",name:"borderColor",onaction:i()},{label:"Background color",type:"colorbox",name:"backgroundColor",onaction:i()}]}]}}function a(e){return e?e.replace(/px$/,""):""}function s(e){return/^[0-9]+$/.test(e)&&(e+="px"),e}function l(e){n("left center right".split(" "),function(t){r.formatter.remove("align"+t,{},e)})}function c(e){n("top middle bottom".split(" "),function(t){r.formatter.remove("valign"+t,{},e)})}function u(t,n,r){function i(t,r){return r=r||[],e.each(t,function(e){var t={text:e.text||e.title};e.menu?t.menu=i(e.menu):(t.value=e.value,n&&n(t)),r.push(t)}),r}return i(t,r||[])}function d(e,t,n){var r=t.toJSON(),i=e.parseStyle(r.style);n?(t.find("#borderColor").value(i["border-color"]||"")[0].fire("change"),t.find("#backgroundColor").value(i["background-color"]||"")[0].fire("change")):(i["border-color"]=r.borderColor,i["background-color"]=r.backgroundColor),t.find("#style").value(e.serializeStyle(e.parseStyle(e.serializeStyle(i))))}function f(e,t,n){var r=e.parseStyle(e.getAttrib(n,"style"));r["border-color"]&&(t.borderColor=r["border-color"]),r["background-color"]&&(t.backgroundColor=r["background-color"]),t.style=e.serializeStyle(r)}function p(e,t,r){var i=e.parseStyle(e.getAttrib(t,"style"));n(r,function(e){i[e.name]=e.value}),e.setAttrib(t,"style",e.serializeStyle(e.parseStyle(e.serializeStyle(i))))}var m=this;m.tableProps=function(){m.table(!0)},m.table=function(i){function c(){function n(e,t,r){if("TD"===e.tagName||"TH"===e.tagName)x.setStyle(e,t,r);else if(e.children)for(var i=0;i<e.children.length;i++)n(e.children[i],t,r)}var i;d(x,this),w=e.extend(w,this.toJSON()),w["class"]===!1&&delete w["class"],r.undoManager.transact(function(){if(h||(h=r.plugins.table.insertTable(w.cols||1,w.rows||1)),r.dom.setAttribs(h,{style:w.style,"class":w["class"]}),r.settings.table_style_by_css){if(C=[],C.push({name:"border",value:w.border}),C.push({name:"border-spacing",value:s(w.cellspacing)}),p(x,h,C),x.setAttribs(h,{"data-mce-border-color":w.borderColor,"data-mce-cell-padding":w.cellpadding,"data-mce-border":w.border}),h.children)for(var e=0;e<h.children.length;e++)n(h.children[e],"border",w.border),n(h.children[e],"padding",s(w.cellpadding))}else r.dom.setAttribs(h,{border:w.border,cellpadding:w.cellpadding,cellspacing:w.cellspacing});x.getAttrib(h,"width")&&!r.settings.table_style_by_css?x.setAttrib(h,"width",a(w.width)):x.setStyle(h,"width",s(w.width)),x.setStyle(h,"height",s(w.height)),i=x.select("caption",h)[0],i&&!w.caption&&x.remove(i),!i&&w.caption&&(i=x.create("caption"),i.innerHTML=t.ie?"\xa0":'<br data-mce-bogus="1"/>',h.insertBefore(i,h.firstChild)),l(h),w.align&&r.formatter.apply("align"+w.align,{},h),r.focus(),r.addVisual()})}function m(e,t){function n(e,n){for(var r=0;r<n.length;r++){var i=x.getStyle(n[r],t);if("undefined"==typeof e&&(e=i),e!=i)return""}return e}var i,o=r.dom.select("td,th",e);return i=n(i,o)}var h,g,v,b,y,C,x=r.dom,w={};i===!0?(h=x.getParent(r.selection.getStart(),"table"),h&&(w={width:a(x.getStyle(h,"width")||x.getAttrib(h,"width")),height:a(x.getStyle(h,"height")||x.getAttrib(h,"height")),cellspacing:a(x.getStyle(h,"border-spacing")||x.getAttrib(h,"cellspacing")),cellpadding:x.getAttrib(h,"data-mce-cell-padding")||x.getAttrib(h,"cellpadding")||m(h,"padding"),border:x.getAttrib(h,"data-mce-border")||x.getAttrib(h,"border")||m(h,"border"),borderColor:x.getAttrib(h,"data-mce-border-color"),caption:!!x.select("caption",h)[0],"class":x.getAttrib(h,"class")},n("left center right".split(" "),function(e){r.formatter.matchNode(h,"align"+e)&&(w.align=e)}))):(g={label:"Cols",name:"cols"},v={label:"Rows",name:"rows"}),r.settings.table_class_list&&(w["class"]&&(w["class"]=w["class"].replace(/\s*mce\-item\-table\s*/g,"")),b={name:"class",type:"listbox",label:"Class",values:u(r.settings.table_class_list,function(e){e.value&&(e.textStyle=function(){return r.formatter.getCssText({block:"table",classes:[e.value]})})})}),y={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",labelGapCalc:!1,padding:0,layout:"grid",columns:2,defaults:{type:"textbox",maxWidth:50},items:r.settings.table_appearance_options!==!1?[g,v,{label:"Width",name:"width"},{label:"Height",name:"height"},{label:"Cell spacing",name:"cellspacing"},{label:"Cell padding",name:"cellpadding"},{label:"Border",name:"border"},{label:"Caption",name:"caption",type:"checkbox"}]:[g,v,{label:"Width",name:"width"},{label:"Height",name:"height"}]},{label:"Alignment",name:"align",type:"listbox",text:"None",values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},b]},r.settings.table_advtab!==!1?(f(x,w,h),r.windowManager.open({title:"Table properties",data:w,bodyType:"tabpanel",body:[{title:"General",type:"form",items:y},o(x)],onsubmit:c})):r.windowManager.open({title:"Table properties",data:w,body:y,onsubmit:c})},m.merge=function(e,t){r.windowManager.open({title:"Merge cells",body:[{label:"Cols",name:"cols",type:"textbox",value:"1",size:10},{label:"Rows",name:"rows",type:"textbox",value:"1",size:10}],onsubmit:function(){var n=this.toJSON();r.undoManager.transact(function(){e.merge(t,n.cols,n.rows)})}})},m.cell=function(){function t(e,t,n){(1===b.length||n)&&v.setAttrib(e,t,n)}function i(e,t,n){(1===b.length||n)&&v.setStyle(e,t,n)}function p(){d(v,this),h=e.extend(h,this.toJSON()),r.undoManager.transact(function(){n(b,function(e){t(e,"scope",h.scope),t(e,"style",h.style),t(e,"class",h["class"]),i(e,"width",s(h.width)),i(e,"height",s(h.height)),h.type&&e.nodeName.toLowerCase()!==h.type&&(e=v.rename(e,h.type)),1===b.length&&(l(e),c(e)),h.align&&r.formatter.apply("align"+h.align,{},e),h.valign&&r.formatter.apply("valign"+h.valign,{},e)}),r.focus()})}var m,h,g,v=r.dom,b=[];if(b=r.dom.select("td[data-mce-selected],th[data-mce-selected]"),m=r.dom.getParent(r.selection.getStart(),"td,th"),!b.length&&m&&b.push(m),m=m||b[0]){b.length>1?h={width:"",height:"",scope:"","class":"",align:"",style:"",type:m.nodeName.toLowerCase()}:(h={width:a(v.getStyle(m,"width")||v.getAttrib(m,"width")),height:a(v.getStyle(m,"height")||v.getAttrib(m,"height")),scope:v.getAttrib(m,"scope"),"class":v.getAttrib(m,"class")},h.type=m.nodeName.toLowerCase(),n("left center right".split(" "),function(e){r.formatter.matchNode(m,"align"+e)&&(h.align=e)}),n("top middle bottom".split(" "),function(e){r.formatter.matchNode(m,"valign"+e)&&(h.valign=e)}),f(v,h,m)),r.settings.table_cell_class_list&&(g={name:"class",type:"listbox",label:"Class",values:u(r.settings.table_cell_class_list,function(e){e.value&&(e.textStyle=function(){return r.formatter.getCssText({block:"td",classes:[e.value]})})})});var y={type:"form",layout:"flex",direction:"column",labelGapCalc:"children",padding:0,items:[{type:"form",layout:"grid",columns:2,labelGapCalc:!1,padding:0,defaults:{type:"textbox",maxWidth:50},items:[{label:"Width",name:"width"},{label:"Height",name:"height"},{label:"Cell type",name:"type",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{label:"Scope",name:"scope",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{label:"H Align",name:"align",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"V Align",name:"valign",type:"listbox",text:"None",minWidth:90,maxWidth:null,values:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}]},g]};r.settings.table_cell_advtab!==!1?r.windowManager.open({title:"Cell properties",bodyType:"tabpanel",data:h,body:[{title:"General",type:"form",items:y},o(v)],onsubmit:p}):r.windowManager.open({title:"Cell properties",data:h,body:y,onsubmit:p})}},m.row=function(){function t(e,t,n){(1===C.length||n)&&y.setAttrib(e,t,n)}function i(e,t,n){(1===C.length||n)&&y.setStyle(e,t,n)}function c(){var o,a,c;d(y,this),v=e.extend(v,this.toJSON()),r.undoManager.transact(function(){var e=v.type;n(C,function(n){t(n,"scope",v.scope),t(n,"style",v.style),t(n,"class",v["class"]),i(n,"height",s(v.height)),e!==n.parentNode.nodeName.toLowerCase()&&(o=y.getParent(n,"table"),a=n.parentNode,c=y.select(e,o)[0],c||(c=y.create(e),o.firstChild?o.insertBefore(c,o.firstChild):o.appendChild(c)),c.appendChild(n),a.hasChildNodes()||y.remove(a)),1===C.length&&l(n),v.align&&r.formatter.apply("align"+v.align,{},n)}),r.focus()})}var p,m,h,g,v,b,y=r.dom,C=[];p=r.dom.getParent(r.selection.getStart(),"table"),m=r.dom.getParent(r.selection.getStart(),"td,th"),n(p.rows,function(e){n(e.cells,function(t){if(y.getAttrib(t,"data-mce-selected")||t==m)return C.push(e),!1})}),h=C[0],h&&(C.length>1?v={height:"",scope:"","class":"",align:"",type:h.parentNode.nodeName.toLowerCase()}:(v={height:a(y.getStyle(h,"height")||y.getAttrib(h,"height")),scope:y.getAttrib(h,"scope"),"class":y.getAttrib(h,"class")},v.type=h.parentNode.nodeName.toLowerCase(),n("left center right".split(" "),function(e){r.formatter.matchNode(h,"align"+e)&&(v.align=e)}),f(y,v,h)),r.settings.table_row_class_list&&(g={name:"class",type:"listbox",label:"Class",values:u(r.settings.table_row_class_list,function(e){e.value&&(e.textStyle=function(){return r.formatter.getCssText({block:"tr",classes:[e.value]})})})}),b={type:"form",columns:2,padding:0,defaults:{type:"textbox"},items:[{type:"listbox",name:"type",label:"Row type",text:"Header",maxWidth:null,values:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"listbox",name:"align",label:"Alignment",text:"None",maxWidth:null,values:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height"},g]},r.settings.table_row_advtab!==!1?r.windowManager.open({title:"Row properties",data:v,bodyType:"tabpanel",body:[{title:"General",type:"form",items:b},o(y)],onsubmit:c}):r.windowManager.open({title:"Row properties",data:v,body:b,onsubmit:c}))}}}),r("tinymce/tableplugin/ResizeBars",["tinymce/util/Tools","tinymce/util/VK"],function(e,n){var r;return function(i){function o(e,t){return{index:e,y:i.dom.getPos(t).y}}function a(e,t){return{index:e,y:i.dom.getPos(t).y+t.offsetHeight}}function s(e,t){return{index:e,x:i.dom.getPos(t).x}}function l(e,t){return{index:e,x:i.dom.getPos(t).x+t.offsetWidth}}function c(){var e=i.getBody().dir;return"rtl"===e}function u(){return i.inline}function d(){return u?i.getBody().ownerDocument.body:i.getBody()}function f(e,t){return c()?l(e,t):s(e,t)}function p(e,t){return c()?s(e,t):l(e,t)}function m(e,t){return h(e,"width")/h(t,"width")*100}function h(e,t){var n=i.dom.getStyle(e,t,!0),r=parseInt(n,10);return r}function g(e){var t=h(e,"width"),n=h(e.parentElement,"width");return t/n*100}function v(e,t){var n=h(e,"width");return t/n*100}function b(e,t){var n=h(e.parentElement,"width");return t/n*100}function y(e,t,n){for(var r=[],i=1;i<n.length;i++){var o=n[i].element;r.push(e(i-1,o))}var a=n[n.length-1];return r.push(t(n.length-1,a.element)),r}function C(){var t=i.dom.select("."+fe,d());e.each(t,function(e){i.dom.remove(e)})}function x(e){C(),B(e)}function w(e,t,n,r,i,o,a,s){var l={"data-mce-bogus":"all","class":fe+" "+e,unselectable:"on","data-mce-resize":!1,style:"cursor: "+t+"; margin: 0; padding: 0; position: absolute; left: "+n+"px; top: "+r+"px; height: "+i+"px; width: "+o+"px; "};return l[a]=s,l}function N(t,n,r){e.each(t,function(e){var t=r.x,o=e.y-xe/2,a=xe,s=n;i.dom.add(d(),"div",w(pe,me,t,o,a,s,he,e.index))})}function E(t,n,r){e.each(t,function(e){var t=e.x-xe/2,o=r.y,a=n,s=xe;i.dom.add(d(),"div",w(ve,be,t,o,a,s,ye,e.index))})}function S(t){return e.map(t.rows,function(t){var n=e.map(t.cells,function(e){var t=e.hasAttribute("rowspan")?parseInt(e.getAttribute("rowspan"),10):1,n=e.hasAttribute("colspan")?parseInt(e.getAttribute("colspan"),10):1;return{element:e,rowspan:t,colspan:n}});return{element:t,cells:n}})}function _(n){function r(e,t){return e+","+t}function i(e,t){return s[r(e,t)]}function o(){var t=[];return e.each(l,function(e){t=t.concat(e.cells)}),t}function a(){return l}var s={},l=[],c=0,u=0;return e.each(n,function(n,i){var o=[];e.each(n.cells,function(e){for(var n=0;s[r(i,n)]!==t;)n++;for(var a={element:e.element,colspan:e.colspan,rowspan:e.rowspan,rowIndex:i,colIndex:n},l=0;l<e.colspan;l++)for(var d=0;d<e.rowspan;d++){var f=i+d,p=n+l;s[r(f,p)]=a,c=Math.max(c,f+1),u=Math.max(u,p+1)}o.push(a)}),l.push({element:n.element,cells:o})}),{grid:{maxRows:c,maxCols:u},getAt:i,getAllCells:o,getAllRows:a}}function k(e,t){for(var n=[],r=e;r<t;r++)n.push(r);return n}function T(e,t,n){for(var r,i=e(),o=0;o<i.length;o++)t(i[o])&&(r=i[o]);return r?r:n()}function R(t){var n=k(0,t.grid.maxCols),r=k(0,t.grid.maxRows);return e.map(n,function(e){function n(){for(var n=[],i=0;i<r.length;i++){var o=t.getAt(i,e);o&&o.colIndex===e&&n.push(o)}return n}function i(e){return 1===e.colspan}function o(){for(var n,i=0;i<r.length;i++)if(n=t.getAt(i,e))return n;return null}return T(n,i,o)})}function A(t){var n=k(0,t.grid.maxCols),r=k(0,t.grid.maxRows);return e.map(r,function(e){function r(){for(var r=[],i=0;i<n.length;i++){var o=t.getAt(e,i);o&&o.rowIndex===e&&r.push(o)}return r}function i(e){return 1===e.rowspan}function o(){return t.getAt(e,0)}return T(r,i,o);
})}function B(e){var t=S(e),n=_(t),r=A(n),s=R(n),l=i.dom.getPos(e),c=r.length>0?y(o,a,r):[],u=s.length>0?y(f,p,s):[];N(c,e.offsetWidth,l),E(u,e.offsetHeight,l)}function P(e,t,n,r){if(t<0||t>=e.length-1)return"";var i=e[t];if(i)i={value:i,delta:0};else for(var o=e.slice(0,t).reverse(),a=0;a<o.length;a++)o[a]&&(i={value:o[a],delta:a+1});var s=e[t+1];if(s)s={value:s,delta:1};else for(var l=e.slice(t+1),c=0;c<l.length;c++)l[c]&&(s={value:l[c],delta:c+1});var u=s.delta-i.delta,d=Math.abs(s.value-i.value)/u;return n?d/h(r,"width")*100:d}function D(e,t){var n=i.dom.getStyle(e,t);return n||(n=i.dom.getAttrib(e,t)),n||(n=i.dom.getStyle(e,t,!0)),n}function M(e,t,n){var r=D(e,"width"),i=parseInt(r,10),o=t?m(e,n):h(e,"width");return(t&&!V(r)||!t&&!$(r))&&(i=0),!isNaN(i)&&i>0?i:o}function L(t,n,r){for(var i=R(t),o=e.map(i,function(e){return f(e.colIndex,e.element).x}),a=[],s=0;s<i.length;s++){var l=i[s].element.hasAttribute("colspan")?parseInt(i[s].element.getAttribute("colspan"),10):1,c=l>1?P(o,s):M(i[s].element,n,r);c=c?c:we,a.push(c)}return a}function I(e){var t=D(e,"height"),n=parseInt(t,10);return V(t)&&(n=0),!isNaN(n)&&n>0?n:h(e,"height")}function O(t){for(var n=A(t),r=e.map(n,function(e){return o(e.rowIndex,e.element).y}),i=[],a=0;a<n.length;a++){var s=n[a].element.hasAttribute("rowspan")?parseInt(n[a].element.getAttribute("rowspan"),10):1,l=s>1?P(r,a):I(n[a].element);l=l?l:Ne,i.push(l)}return i}function H(t,n,r,i,o){function a(t){return e.map(t,function(){return 0})}function s(){var e;if(o)e=[100-d[0]];else{var t=Math.max(i,d[0]+r);e=[t-d[0]]}return e}function l(e,t){var n,o=a(d.slice(0,e)),s=a(d.slice(t+1));if(r>=0){var l=Math.max(i,d[t]-r);n=o.concat([r,l-d[t]]).concat(s)}else{var c=Math.max(i,d[e]+r),u=d[e]-c;n=o.concat([c-d[e],u]).concat(s)}return n}function c(e,t){var n,o=a(d.slice(0,t));if(r>=0)n=o.concat([r]);else{var s=Math.max(i,d[t]+r);n=o.concat([s-d[t]])}return n}var u,d=t.slice(0);return u=0===t.length?[]:1===t.length?s():0===n?l(0,1):n>0&&n<t.length-1?l(n,n+1):n===t.length-1?c(n-1,n):[]}function F(e,t,n){for(var r=0,i=e;i<t;i++)r+=n[i];return r}function z(t,n){var r=t.getAllCells();return e.map(r,function(e){var t=F(e.colIndex,e.colIndex+e.colspan,n);return{element:e.element,width:t,colspan:e.colspan}})}function W(t,n){var r=t.getAllCells();return e.map(r,function(e){var t=F(e.rowIndex,e.rowIndex+e.rowspan,n);return{element:e.element,height:t,rowspan:e.rowspan}})}function U(t,n){var r=t.getAllRows();return e.map(r,function(e,t){return{element:e.element,height:n[t]}})}function V(e){return Se.test(e)}function $(e){return _e.test(e)}function j(t,n,r){function o(t,n){e.each(t,function(e){i.dom.setStyle(e.element,"width",e.width+n),i.dom.setAttrib(e.element,"width",null)})}function a(){return r<u.grid.maxCols-1?g(t):g(t)+b(t,n)}function s(){return r<u.grid.maxCols-1?h(t,"width"):h(t,"width")+n}function l(e,n,o){r!=u.grid.maxCols-1&&o||(i.dom.setStyle(t,"width",e+n),i.dom.setAttrib(t,"width",null))}for(var c=S(t),u=_(c),d=V(t.width)||V(t.style.width),f=L(u,d,t),p=d?v(t,n):n,m=H(f,r,p,we,d,t),y=[],C=0;C<m.length;C++)y.push(m[C]+f[C]);var x=z(u,y),w=d?"%":"px",N=d?a():s();i.undoManager.transact(function(){o(x,w),l(N,w,d)})}function q(t,n,r){for(var o=S(t),a=_(o),s=O(a),l=[],c=0,u=0;u<s.length;u++)l.push(u===r?n+s[u]:s[u]),c+=c[u];var d=W(a,l),f=U(a,l);i.undoManager.transact(function(){e.each(f,function(e){i.dom.setStyle(e.element,"height",e.height+"px"),i.dom.setAttrib(e.element,"height",null)}),e.each(d,function(e){i.dom.setStyle(e.element,"height",e.height+"px"),i.dom.setAttrib(e.element,"height",null)}),i.dom.setStyle(t,"height",c+"px"),i.dom.setAttrib(t,"height",null)})}function Y(){ae=setTimeout(function(){J()},200)}function X(){clearTimeout(ae)}function K(){var e=document.createElement("div");return e.setAttribute("style","margin: 0; padding: 0; position: fixed; left: 0px; top: 0px; height: 100%; width: 100%;"),e.setAttribute("data-mce-bogus","all"),e}function G(e,t){i.dom.bind(e,"mouseup",function(){J()}),i.dom.bind(e,"mousemove",function(e){X(),se&&t(e)}),i.dom.bind(e,"mouseout",function(){Y()})}function J(){if(i.dom.remove(le),se){i.dom.removeClass(ce,Ee),se=!1;var e,t;if(Z(ce)){var n=parseInt(i.dom.getAttrib(ce,Ce),10),o=i.dom.getPos(ce).x;e=parseInt(i.dom.getAttrib(ce,ye),10),t=c()?n-o:o-n,Math.abs(t)>=1&&j(r,t,e)}else if(ee(ce)){var a=parseInt(i.dom.getAttrib(ce,ge),10),s=i.dom.getPos(ce).y;e=parseInt(i.dom.getAttrib(ce,he),10),t=s-a,Math.abs(t)>=1&&q(r,t,e)}x(r),i.nodeChanged()}}function Q(e,t){le=le?le:K(),se=!0,i.dom.addClass(e,Ee),ce=e,G(le,t),i.dom.add(d(),le)}function Z(e){return i.dom.hasClass(e,ve)}function ee(e){return i.dom.hasClass(e,pe)}function te(e){ue=ue!==t?ue:e.clientX;var n=e.clientX-ue;ue=e.clientX;var r=i.dom.getPos(ce).x;i.dom.setStyle(ce,"left",r+n+"px")}function ne(e){de=de!==t?de:e.clientY;var n=e.clientY-de;de=e.clientY;var r=i.dom.getPos(ce).y;i.dom.setStyle(ce,"top",r+n+"px")}function re(e){ue=t,Q(e,te)}function ie(e){de=t,Q(e,ne)}function oe(e){var t=e.target,n=i.getBody();if(i.$.contains(n,r)||r===n)if(Z(t)){e.preventDefault();var o=i.dom.getPos(t).x;i.dom.setAttrib(t,Ce,o),re(t)}else if(ee(t)){e.preventDefault();var a=i.dom.getPos(t).y;i.dom.setAttrib(t,ge,a),ie(t)}else C()}var ae,se,le,ce,ue,de,fe="mce-resize-bar",pe="mce-resize-bar-row",me="row-resize",he="data-row",ge="data-initial-top",ve="mce-resize-bar-col",be="col-resize",ye="data-col",Ce="data-initial-left",xe=4,we=10,Ne=10,Ee="mce-resize-bar-dragging",Se=new RegExp(/(\d+(\.\d+)?%)/),_e=new RegExp(/px|em/);return i.on("init",function(){i.dom.bind(d(),"mousedown",oe)}),i.on("ObjectResized",function(t){var n=t.target;if("TABLE"===n.nodeName){var r=[];e.each(n.rows,function(t){e.each(t.cells,function(e){var t=i.dom.getStyle(e,"width",!0);r.push({cell:e,width:t})})}),e.each(r,function(e){i.dom.setStyle(e.cell,"width",e.width),i.dom.setAttrib(e.cell,"width",null)})}}),i.on("mouseover",function(e){if(!se){var t=i.dom.getParent(e.target,"table");("TABLE"===e.target.nodeName||t)&&(r=t,x(t))}}),i.on("keydown",function(e){switch(e.keyCode){case n.LEFT:case n.RIGHT:case n.UP:case n.DOWN:C()}}),i.on("remove",function(){C(),i.dom.unbind(d(),"mousedown",oe)}),{adjustWidth:j,adjustHeight:q,clearBars:C,drawBars:B,determineDeltas:H,getTableGrid:_,getTableDetails:S,getWidths:L,getPixelHeights:O,isPercentageBasedSize:V,isPixelBasedSize:$,recalculateWidths:z,recalculateCellHeights:W,recalculateRowHeights:U}}}),r("tinymce/tableplugin/Plugin",["tinymce/tableplugin/TableGrid","tinymce/tableplugin/Quirks","tinymce/tableplugin/CellSelection","tinymce/tableplugin/Dialogs","tinymce/tableplugin/ResizeBars","tinymce/util/Tools","tinymce/dom/TreeWalker","tinymce/Env","tinymce/PluginManager"],function(e,t,n,r,i,o,a,s,l){function c(o){function a(e){return function(){o.execCommand(e)}}function l(e,t){var n,r,i,a;for(i='<table id="__mce"><tbody>',n=0;n<t;n++){for(i+="<tr>",r=0;r<e;r++)i+="<td>"+(s.ie&&s.ie<10?"&nbsp;":"<br>")+"</td>";i+="</tr>"}return i+="</tbody></table>",o.undoManager.transact(function(){o.insertContent(i),a=o.dom.get("__mce"),o.dom.setAttrib(a,"id",null),o.$("tr",a).each(function(e,t){o.fire("newrow",{node:t}),o.$("th,td",t).each(function(e,t){o.fire("newcell",{node:t})})}),o.dom.setAttribs(a,o.settings.table_default_attributes||{}),o.dom.setStyles(a,o.settings.table_default_styles||{})}),a}function c(e,t,n){function r(){var r,i,a,s={},l=0;i=o.dom.select("td[data-mce-selected],th[data-mce-selected]"),r=i[0],r||(r=o.selection.getStart()),n&&i.length>0?(u(i,function(e){return s[e.parentNode.parentNode.nodeName]=1}),u(s,function(e){l+=e}),a=1!==l):a=!o.dom.getParent(r,t),e.disabled(a),o.selection.selectorChanged(t,function(t){e.disabled(!t)})}o.initialized?r():o.on("init",r)}function d(){c(this,"table")}function f(){c(this,"td,th")}function p(){c(this,"td,th",!0)}function m(){var e="";e='<table role="grid" class="mce-grid mce-grid-border" aria-readonly="true">';for(var t=0;t<10;t++){e+="<tr>";for(var n=0;n<10;n++)e+='<td role="gridcell" tabindex="-1"><a id="mcegrid'+(10*t+n)+'" href="#" data-mce-x="'+n+'" data-mce-y="'+t+'"></a></td>';e+="</tr>"}return e+="</table>",e+='<div class="mce-text-center" role="presentation">1 x 1</div>'}function h(e,t,n){var r,i,a,s,l,c=n.getEl().getElementsByTagName("table")[0],u=n.isRtl()||"tl-tr"==n.parent().rel;for(c.nextSibling.innerHTML=e+1+" x "+(t+1),u&&(e=9-e),i=0;i<10;i++)for(r=0;r<10;r++)s=c.rows[i].childNodes[r].firstChild,l=(u?r>=e:r<=e)&&i<=t,o.dom.toggleClass(s,"mce-active",l),l&&(a=s);return a.parentNode}function g(){o.addButton("tableprops",{title:"Table properties",onclick:E.tableProps,icon:"table"}),o.addButton("tabledelete",{title:"Delete table",onclick:a("mceTableDelete")}),o.addButton("tablecellprops",{title:"Cell properties",onclick:a("mceTableCellProps")}),o.addButton("tablemergecells",{title:"Merge cells",onclick:a("mceTableMergeCells")}),o.addButton("tablesplitcells",{title:"Split cell",onclick:a("mceTableSplitCells")}),o.addButton("tableinsertrowbefore",{title:"Insert row before",onclick:a("mceTableInsertRowBefore")}),o.addButton("tableinsertrowafter",{title:"Insert row after",onclick:a("mceTableInsertRowAfter")}),o.addButton("tabledeleterow",{title:"Delete row",onclick:a("mceTableDeleteRow")}),o.addButton("tablerowprops",{title:"Row properties",onclick:a("mceTableRowProps")}),o.addButton("tablecutrow",{title:"Cut row",onclick:a("mceTableCutRow")}),o.addButton("tablecopyrow",{title:"Copy row",onclick:a("mceTableCopyRow")}),o.addButton("tablepasterowbefore",{title:"Paste row before",onclick:a("mceTablePasteRowBefore")}),o.addButton("tablepasterowafter",{title:"Paste row after",onclick:a("mceTablePasteRowAfter")}),o.addButton("tableinsertcolbefore",{title:"Insert column before",onclick:a("mceTableInsertColBefore")}),o.addButton("tableinsertcolafter",{title:"Insert column after",onclick:a("mceTableInsertColAfter")}),o.addButton("tabledeletecol",{title:"Delete column",onclick:a("mceTableDeleteCol")})}function v(e){var t=o.dom.is(e,"table")&&o.getBody().contains(e);return t}function b(){var e=o.settings.table_toolbar;""!==e&&e!==!1&&(e||(e="tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol"),o.addContextToolbar(v,e))}function y(){return x}function C(e){x=e}var x,w,N=this,E=new r(o);!o.settings.object_resizing||o.settings.table_resize_bars===!1||o.settings.object_resizing!==!0&&"table"!==o.settings.object_resizing||(w=i(o)),o.settings.table_grid===!1?o.addMenuItem("inserttable",{text:"Table",icon:"table",context:"table",onclick:E.table}):o.addMenuItem("inserttable",{text:"Table",icon:"table",context:"table",ariaHideMenu:!0,onclick:function(e){e.aria&&(this.parent().hideAll(),e.stopImmediatePropagation(),E.table())},onshow:function(){h(0,0,this.menu.items()[0])},onhide:function(){var e=this.menu.items()[0].getEl().getElementsByTagName("a");o.dom.removeClass(e,"mce-active"),o.dom.addClass(e[0],"mce-active")},menu:[{type:"container",html:m(),onPostRender:function(){this.lastX=this.lastY=0},onmousemove:function(e){var t,n,r=e.target;"A"==r.tagName.toUpperCase()&&(t=parseInt(r.getAttribute("data-mce-x"),10),n=parseInt(r.getAttribute("data-mce-y"),10),(this.isRtl()||"tl-tr"==this.parent().rel)&&(t=9-t),t===this.lastX&&n===this.lastY||(h(t,n,e.control),this.lastX=t,this.lastY=n))},onclick:function(e){var t=this;"A"==e.target.tagName.toUpperCase()&&(e.preventDefault(),e.stopPropagation(),t.parent().cancel(),o.undoManager.transact(function(){l(t.lastX+1,t.lastY+1)}),o.addVisual())}}]}),o.addMenuItem("tableprops",{text:"Table properties",context:"table",onPostRender:d,onclick:E.tableProps}),o.addMenuItem("deletetable",{text:"Delete table",context:"table",onPostRender:d,cmd:"mceTableDelete"}),o.addMenuItem("cell",{separator:"before",text:"Cell",context:"table",menu:[{text:"Cell properties",onclick:a("mceTableCellProps"),onPostRender:f},{text:"Merge cells",onclick:a("mceTableMergeCells"),onPostRender:p},{text:"Split cell",onclick:a("mceTableSplitCells"),onPostRender:f}]}),o.addMenuItem("row",{text:"Row",context:"table",menu:[{text:"Insert row before",onclick:a("mceTableInsertRowBefore"),onPostRender:f},{text:"Insert row after",onclick:a("mceTableInsertRowAfter"),onPostRender:f},{text:"Delete row",onclick:a("mceTableDeleteRow"),onPostRender:f},{text:"Row properties",onclick:a("mceTableRowProps"),onPostRender:f},{text:"-"},{text:"Cut row",onclick:a("mceTableCutRow"),onPostRender:f},{text:"Copy row",onclick:a("mceTableCopyRow"),onPostRender:f},{text:"Paste row before",onclick:a("mceTablePasteRowBefore"),onPostRender:f},{text:"Paste row after",onclick:a("mceTablePasteRowAfter"),onPostRender:f}]}),o.addMenuItem("column",{text:"Column",context:"table",menu:[{text:"Insert column before",onclick:a("mceTableInsertColBefore"),onPostRender:f},{text:"Insert column after",onclick:a("mceTableInsertColAfter"),onPostRender:f},{text:"Delete column",onclick:a("mceTableDeleteCol"),onPostRender:f}]});var S=[];u("inserttable tableprops deletetable | cell row column".split(" "),function(e){"|"==e?S.push({text:"-"}):S.push(o.menuItems[e])}),o.addButton("table",{type:"menubutton",title:"Table",menu:S}),s.isIE||o.on("click",function(e){e=e.target,"TABLE"===e.nodeName&&(o.selection.select(e),o.nodeChanged())}),N.quirks=new t(o),o.on("Init",function(){N.cellSelection=new n(o,function(e){e&&w&&w.clearBars()}),N.resizeBars=w}),o.on("PreInit",function(){o.serializer.addAttributeFilter("data-mce-cell-padding,data-mce-border,data-mce-border-color",function(e,t){for(var n=e.length;n--;)e[n].attr(t,null)})}),u({mceTableSplitCells:function(e){e.split()},mceTableMergeCells:function(e){var t;t=o.dom.getParent(o.selection.getStart(),"th,td"),o.dom.select("td[data-mce-selected],th[data-mce-selected]").length?e.merge():E.merge(e,t)},mceTableInsertRowBefore:function(e){e.insertRows(!0)},mceTableInsertRowAfter:function(e){e.insertRows()},mceTableInsertColBefore:function(e){e.insertCols(!0)},mceTableInsertColAfter:function(e){e.insertCols()},mceTableDeleteCol:function(e){e.deleteCols()},mceTableDeleteRow:function(e){e.deleteRows()},mceTableCutRow:function(e){x=e.cutRows()},mceTableCopyRow:function(e){x=e.copyRows()},mceTablePasteRowBefore:function(e){e.pasteRows(x,!0)},mceTablePasteRowAfter:function(e){e.pasteRows(x)},mceSplitColsBefore:function(e){e.splitCols(!0)},mceSplitColsAfter:function(e){e.splitCols(!1)},mceTableDelete:function(e){w&&w.clearBars(),e.deleteTable()}},function(t,n){o.addCommand(n,function(){var n=new e(o);n&&(t(n),o.execCommand("mceRepaint"),N.cellSelection.clear())})}),u({mceInsertTable:E.table,mceTableProps:function(){E.table(!0)},mceTableRowProps:E.row,mceTableCellProps:E.cell},function(e,t){o.addCommand(t,function(t,n){e(n)})}),g(),b(),o.settings.table_tab_navigation!==!1&&o.on("keydown",function(t){var n,r,i;9==t.keyCode&&(n=o.dom.getParent(o.selection.getStart(),"th,td"),n&&(t.preventDefault(),r=new e(o),i=t.shiftKey?-1:1,o.undoManager.transact(function(){!r.moveRelIdx(n,i)&&i>0&&(r.insertRow(),r.refresh(),r.moveRelIdx(n,i))})))}),N.insertTable=l,N.setClipboardRows=C,N.getClipboardRows=y}var u=o.each;l.add("table",c)})}(window);
