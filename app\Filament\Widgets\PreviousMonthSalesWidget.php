<?php

namespace App\Filament\Widgets;

use App\Models\Sale;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Carbon\Carbon;

class PreviousMonthSalesWidget extends BaseWidget
{
    protected static ?int $sort = 110;

    protected function getStats(): array
    {
        // Mês anterior
        $now = Carbon::now();
        $previousMonth = $now->copy()->startOfMonth()->subMonth();
        $endOfPreviousMonth = $previousMonth->copy()->endOfMonth();

        $previousMonthSales = Sale::whereBetween('sale_date', [$previousMonth, $endOfPreviousMonth])->get();

        $totalQuantity = $previousMonthSales->sum('quantity');
        $totalValue = $previousMonthSales->sum('total');

        // Dois meses atrás para comparação
        $twoMonthsAgo = $now->copy()->startOfMonth()->subMonths(2);
        $endOfTwoMonthsAgo = $twoMonthsAgo->copy()->endOfMonth();

        $twoMonthsAgoSales = Sale::whereBetween('sale_date', [$twoMonthsAgo, $endOfTwoMonthsAgo])->get();
        $twoMonthsAgoQuantity = $twoMonthsAgoSales->sum('quantity');
        $twoMonthsAgoValue = $twoMonthsAgoSales->sum('total');

        // Calcular variação percentual
        $quantityChange = $twoMonthsAgoQuantity > 0
            ? (($totalQuantity - $twoMonthsAgoQuantity) / $twoMonthsAgoQuantity) * 100
            : 0;

        $valueChange = $twoMonthsAgoValue > 0
            ? (($totalValue - $twoMonthsAgoValue) / $twoMonthsAgoValue) * 100
            : 0;

        $monthName = $previousMonth->locale('pt_BR')->isoFormat('MMMM YYYY');

        return [
            Stat::make('Quantidade de Vendas - ' . $monthName, number_format($totalQuantity, 0, ',', '.'))
                ->description($quantityChange >= 0
                    ? '+' . number_format($quantityChange, 1) . '% em relação ao mês anterior'
                    : number_format($quantityChange, 1) . '% em relação ao mês anterior'
                )
                ->descriptionIcon($quantityChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($quantityChange >= 0 ? 'success' : 'danger')
                ->chart([3, 7, 5, 12, 8, 9, 14]),

            Stat::make('Valor Total - ' . $monthName, 'R$ ' . number_format($totalValue, 2, ',', '.'))
                ->description($valueChange >= 0
                    ? '+' . number_format($valueChange, 1) . '% em relação ao mês anterior'
                    : number_format($valueChange, 1) . '% em relação ao mês anterior'
                )
                ->descriptionIcon($valueChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($valueChange >= 0 ? 'success' : 'danger')
                ->chart([3, 7, 5, 12, 8, 9, 14]),
        ];
    }
}
