body {
  padding: 10px;
  overflow:hidden;
}

.mce-panel {
	border:0 solid #9e9e9e;
	background-color:#ffffff;
	zoom:1
}

a:focus, a:active {
  outline: 0;
}

a {
  text-decoration: none;
  color: #555;
}

a img {
  border: none;
}

#preview {
  width: 300px;
  height: 240px;
  float: right;
}

.listbox {
  width: 400px;
}

.listboxText {
  width: 225px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

ul {
  list-style-type: none;
}

ul.autocomplete {
  display: none;
  width: 300px;
  padding: 10px;
  position: absolute;
  top: 40px;
  z-index: 9999;
  left: 132px;
  background: #fff;
  border: 1px solid #e4e4e4;
}

ul.autocomplete li a {
  display: block;
  padding: 5px 10px;
  font-size: 11px;
  color: #333;
}

ul.autocomplete li a:hover {
  background: #f5f5f5;
}

#load_more {
  text-align: center;
  padding-bottom: 20px;
  display: none;
}

#divScroll {
  height: 380px;
  overflow: auto;
  background: #FFF;
  border: #ccc 1px solid;
  margin: 0 0 10px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}

.videos {
  margin: 0;
  padding: 0;
}

.videos.preloader {
  background: url() no-repeat center;
  height: 370px;
}

ul.videos li {
  float: left;
  width: 98%;
  background: #eee;
  margin: 1%;
  padding: 10px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
}

ul.videos li p {
  margin: 0;
  padding: 0;
}

.videos small {
  display: block;
  font-weight: 400;
  color: #ACACAC;
}
.insert{
    margin-left:10px;
}

.videos img {
  max-width: 137px;
}
