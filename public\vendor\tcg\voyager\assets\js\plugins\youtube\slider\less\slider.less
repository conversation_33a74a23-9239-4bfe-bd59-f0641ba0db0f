/*!
 * Slider for Bootstrap
 *
 * Copyright 2012 <PERSON>
 * Licensed under the Apache License v2.0
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 */
//@import '../bower_components/bootstrap/less/variables.less';
@line-height-base: 20px;
@border-radius-base: 4px;
@import 'mixins.less';

.slider {
	display: inline-block;
	vertical-align: middle;
	position: relative;
	&.slider-horizontal {
		width: 210px;
		height: @line-height-base;
		.slider-track {
			height: @line-height-base/2;
			width: 100%;
			margin-top: -@line-height-base/4;
			top: 50%;
			left: 0;
		}
		.slider-selection {
			height: 100%;
			top: 0;
			bottom: 0;
		}
		.slider-handle {
			margin-left: -@line-height-base/2;
			margin-top: -@line-height-base/4;
			&.triangle {
    			border-width: 0 @line-height-base/2 @line-height-base/2 @line-height-base/2;
				width: 0;
				height: 0;
				border-bottom-color: #0480be;
				margin-top: 0;
			}
		}
	}
	&.slider-vertical {
		height: 210px;
		width: @line-height-base;
		.slider-track {
			width: @line-height-base/2;
			height: 100%;
			margin-left: -@line-height-base/4;
			left: 50%;
			top: 0;
		}
		.slider-selection {
			width: 100%;
			left: 0;
			top: 0;
			bottom: 0;
		}
		.slider-handle {
			margin-left: -@line-height-base/4;
			margin-top: -@line-height-base/2;
			&.triangle {
				border-width: @line-height-base/2 0 @line-height-base/2 @line-height-base/2;
				width: 1px;
				height: 1px;
				border-left-color: #0480be;
				margin-left: 0;
			}
		}
	}
	&.slider-disabled {
		.slider-handle {
			#gradient > .vertical(#dfdfdf, #bebebe);
		}
		.slider-track {
			#gradient > .vertical(#e5e5e5, #e9e9e9);
		}
	}
	input {
		display: none;
	}
	.tooltip-inner {
		white-space: nowrap;
	}
}
.slider-track {
	position: absolute;
	cursor: pointer;
	#gradient > .vertical(#f5f5f5, #f9f9f9);
	.box-shadow(inset 0 1px 2px rgba(0,0,0,.1));
	border-radius: @border-radius-base;
}
.slider-selection {
	position: absolute;
	#gradient > .vertical(#f9f9f9, #f5f5f5);
	.box-shadow(inset 0 -1px 0 rgba(0,0,0,.15));
	.box-sizing(border-box);
	border-radius: @border-radius-base;
}
.slider-handle {
	position: absolute;
	width: @line-height-base;
	height: @line-height-base;
	#gradient > .vertical(#149bdf, #0480be);
	.box-shadow(~"inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05)");
	opacity: 0.8;
	border: 0px solid transparent;
	&.round {
		border-radius: 50%;
	}
	&.triangle {
		background: transparent none;
	}
}
