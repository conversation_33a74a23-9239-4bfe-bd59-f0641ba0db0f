<?php

namespace App\Filament\Resources\ChargeConfigResource\Pages;

use App\Filament\Resources\ChargeConfigResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditChargeConfig extends EditRecord
{
    protected static string $resource = ChargeConfigResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }
}
