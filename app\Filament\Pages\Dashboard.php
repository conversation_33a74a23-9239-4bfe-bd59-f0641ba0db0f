<?php

namespace App\Filament\Pages;

use Filament\Panel;

class Dashboard extends \Filament\Pages\Dashboard
{
    protected static ?string $navigationIcon = 'phosphor-house-duotone';

    public function getWidgets(): array
    {
        return [
            \App\Filament\Widgets\CurrentMonthSalesWidget::class,
            \App\Filament\Widgets\PreviousMonthSalesWidget::class,
            \App\Filament\Widgets\PendingSalesWidget::class,
        ];
    }

    public function panel(Panel $panel): Panel
    {
        return $panel
            ->pages([])
            ->globalSearch(true);
    }
}
