<?php

return [
    'date' => [
        'last_week' => '<PERSON><PERSON><PERSON> Woche',
        'last_year' => '<PERSON>ztes Jahr',
        'this_week' => '<PERSON><PERSON>',
        'this_year' => '<PERSON><PERSON>',
    ],

    'generic' => [
        'action'                 => 'Aktion',
        'actions'                => 'Aktionen',
        'add'                    => 'Hinzufügen',
        'add_folder'             => 'Ordner Hinzufügen',
        'add_new'                => 'Neu Hinzufügen',
        'all_done'               => 'Alles erledigt',
        'are_you_sure'           => 'Sind Sie sicher',
        'are_you_sure_delete'    => 'Sind Sie sicher dass sie löschen möchten',
        'auto_increment'         => 'Automatische Werterhöhung',
        'browse'                 => 'Browse',
        'builder'                => 'Builder',
        'bulk_delete'            => 'Massenlöschung',
        'bulk_delete_confirm'    => 'Ja, alle löschen',
        'bulk_delete_nothing'    => 'Sie haben nichts ausgewählt',
        'cancel'                 => 'Abbruch',
        'choose_type'            => 'Typ auswählen',
        'click_here'             => 'Hier Klicken',
        'close'                  => 'Schließen',
        'compass'                => 'Kompass',
        'created_at'             => 'Angelegt',
        'custom'                 => 'Custom',
        'dashboard'              => 'Dashboard',
        'database'               => 'Datenbank',
        'default'                => 'Defaultwert',
        'delete'                 => 'Löschen',
        'delete_confirm'         => 'Ja, Löschen!',
        'delete_question'        => 'Wirklich Löschen',
        'delete_this_confirm'    => 'Ja, Löschen',
        'deselect_all'           => 'Alles abwählen',
        'download'               => 'Herunterladen',
        'edit'                   => 'Editieren',
        'email'                  => 'E-Mail',
        'error_deleting'         => 'Es gab ein Problem beim Versuch dies zu Löschen',
        'exception'              => 'Exception',
        'featured'               => 'Featured',
        'field_does_not_exist'   => 'Feld existiert nicht',
        'how_to_use'             => 'Bedieneranleitung',
        'index'                  => 'Index',
        'internal_error'         => 'Interner Fehler',
        'items'                  => 'Element(e)',
        'keep_sidebar_open'      => 'Yarr! Anker werfen! (und Sidebar geöffnet lassen)',
        'key'                    => 'Key',
        'last_modified'          => 'Zuletzt modifiziert',
        'length'                 => 'Länge',
        'login'                  => 'Login',
        'media'                  => 'Medien',
        'menu_builder'           => 'Menü Editor',
        'move'                   => 'Verschieben',
        'name'                   => 'Name',
        'new'                    => 'Neu',
        'no'                     => 'Nein',
        'no_thanks'              => 'Nein Danke',
        'not_null'               => 'Not Null',
        'options'                => 'Optionen',
        'password'               => 'Passwort',
        'permissions'            => 'Rechte',
        'profile'                => 'Profil',
        'public_url'             => 'Öffentliche URL',
        'read'                   => 'Lesen',
        'rename'                 => 'Umbenennen',
        'required'               => 'Notwendig',
        'return_to_list'         => 'Zurück zur Liste',
        'route'                  => 'Route',
        'save'                   => 'Speichern',
        'search'                 => 'Suchen',
        'select_all'             => 'Alles Auswählen',
        'select_group'           => 'Bestehende Gruppe auswählen oder neue Gruppe hinzufügen',
        'settings'               => 'Einstellungen',
        'showing_entries'        => 'Zeige :from bis :to von :all Eintrag|Zeige :from bis :to von :all Einträgen',
        'submit'                 => 'Absenden',
        'successfully_added_new' => 'Erfolgreich neu hinzugefügt',
        'successfully_deleted'   => 'Erfolgreich gelöscht',
        'successfully_updated'   => 'Erfolgreich Editiert',
        'timestamp'              => 'Zeitstempel',
        'title'                  => 'Titel',
        'type'                   => 'Typ',
        'unsigned'               => 'Unsigned',
        'unstick_sidebar'        => 'Sidebar ablösen',
        'update'                 => 'Aktualisierung',
        'update_failed'          => 'Aktualisierung fehlgeschlagen',
        'upload'                 => 'Upload',
        'url'                    => 'URL',
        'view'                   => 'Anzeige',
        'viewing'                => 'Anzeigen',
        'yes'                    => 'Ja',
        'yes_please'             => 'Ja, Bitte',
    ],

    'login' => [
        'loggingin'    => 'Einloggen',
        'signin_below' => 'Unten anmelden:',
        'welcome'      => 'Willkommen bei Voyager. Der fehlende Admin für Laravel',
    ],

    'profile' => [
        'avatar'        => 'Avatar',
        'edit'          => 'Mein Profil Editieren',
        'edit_user'     => 'Benutzer Editieren',
        'password'      => 'Passwort',
        'password_hint' => 'Leer lassen um das Bisherige zu behalten',
        'role'          => 'Rolle',
        'user_role'     => 'Benutzerrolle',
    ],

    'settings' => [
        'usage_help'           => 'Sie können den Wert jeder Einstellung überall auf der Seite erhalten durch den Aufruf',
        'save'                 => 'Einstellungen Speichern',
        'new'                  => 'Neue Einstellung',
        'help_name'            => 'Einstellung Name Beispiel: Admin Titel',
        'help_key'             => 'Einstellung Schlüssel Beispiel: admin_title',
        'help_option'          => '(optional, betrifft lediglich bestimmte Typen wie Dropdown Box oder Radio Button)',
        'add_new'              => 'Neue Einstellung Hinzufügen',
        'delete_question'      => 'Wollen Sie die Einstellung :setting wirklich Löschen?',
        'delete_confirm'       => 'Ja, diese Einstellung Löschen',
        'successfully_created' => 'Einstellungen erfolgreich erstellt',
        'successfully_saved'   => 'Einstellungen erfolgreich gespeichert',
        'successfully_deleted' => 'Einstellungen erfolgreich gelöscht',
        'already_at_top'       => 'Dies ist bereits an erster Stelle der Liste',
        'already_at_bottom'    => 'Dies ist bereits an letzter Stelle der Liste',
        'key_already_exists'   => 'Der Schlüssel :key existiert bereits',
        'moved_order_up'       => 'Einstellung :name wurde nach oben geschoben',
        'moved_order_down'     => 'Einstellung :name wurde nach unten geschoben',
        'successfully_removed' => 'Wert :name wurde erfolgreich gelöscht',
        'group_general'        => 'General',
        'group_admin'          => 'Admin',
        'group_site'           => 'Site',
        'group'                => 'Gruppe',
        'help_group'           => 'Diese Einstellung ist zugewiesen zu',
    ],

    'media' => [
        'add_new_folder'         => 'Neuen Ordner Hinzufügen',
        'audio_support'          => 'Ihr Browser unterstützt das Audio Element nicht.',
        'create_new_folder'      => 'Neuen Ordner Erstellen',
        'delete_folder_question' => 'Das Löschen des Ordners wird alle darin enthaltenen Dateien und Ordnder löschen.',
        'destination_folder'     => 'Ziel Ordner',
        'drag_drop_info'         => 'Dateien mit Drag und Drop hineinziehen oder unten klicken um hochzuladen',
        'error_already_exists'   => 'Es ist bereits eine Datei bzw. ein Ordner mit diesem Namen in diesem Ordner enthalten.',
        'error_creating_dir'     => 'Beim Versuch das Verzeichnis Anzulegen ist ein Fehler aufgetreten. '.
                                    'Stellen Sie sicher, dass Sie ausreichende Zugriffsrechte dafür haben.',
        'error_deleting_file' => 'Beim Versuch diese Datei zu Löschen ist ein Fehler aufgetreten. '.
                                    'Stellen Sie sicher, dass Sie ausreichende Zugriffsrechte dafür haben.',
        'error_deleting_folder' => 'Beim Versuch diesen Ordner zu Löschen ist ein Fehler aufgetreten. Stellen Sie'.
                                    'sicher, dass Sie ausreichende Zugriffsrechte dafür haben.',
        'error_may_exist' => 'Datei oder Ordner unter diesem Namen können bereits existieren. Wählen Sie '.
                                    'einen anderen Namen oder Löschen Sie die andere Datei.',
        'error_moving' => 'Beim Versuch diese Datei bzw. Ordner zu Verschieben ist ein Fehler aufgetreten. '.
                                    'Stellen Sie sicher, dass Sie ausreichende Zugriffsrechte dafür haben.',
        'error_uploading'       => 'Hochladen Fehlgeschlagen: Unbekannter Fehler aufgetreten!',
        'folder_exists_already' => 'Dieser Ordner existiert bereits. Bitte Löschen Sie diesen Ordner falls Sie ihn '.
                                    'neu Anlegen möchten',
        'image_does_not_exist'  => 'Bild existiert nicht',
        'image_removed'         => 'Bild entfernt',
        'library'               => 'Medien Bibliothek',
        'loading'               => 'LADE IHRE MEDIEN DATEIEN',
        'move_file_folder'      => 'Datei/Ordner Verschieben',
        'new_file_folder'       => 'Datei/Ordner Anlegen',
        'new_folder_name'       => 'Name des neuen Ordners',
        'no_files_here'         => 'Hier sind keine Dateien vorhanden.',
        'no_files_in_folder'    => 'Keine Dateien in diesem Ordner.',
        'nothing_selected'      => 'Keine Datei oder Ordner angewählt',
        'rename_file_folder'    => 'Datei/Ordner Umbenennen',
        'success_uploaded_file' => 'Neue Datei erfolgreich hochgeladen!',
        'success_uploading'     => 'Bild erfolgreich hochgeladen!',
        'uploading_wrong_type'  => 'Fehler beim Hochladen: Nicht unterstütztes Dateiformat oder Datei zu groß '.
                                    'zum Hochladen',
        'video_support' => 'Ihr Browser unterstützt den Video Tag nicht.',
    ],

    'menu_builder' => [
        'color'                => 'Farbe in RGB oder hex (optional)',
        'color_ph'             => 'Farbe (z. B. #ffffff oder rgb(255, 255, 255)',
        'create_new_item'      => 'Erstelle einen neues Menü Element',
        'delete_item_confirm'  => 'Ja, Lösche dieses Menü Element',
        'delete_item_question' => 'Sind Sie sicher dass Sie dieses Menü Element Löschen möchten?',
        'drag_drop_info'       => 'Sie können die Reihenfolge der untenstehenden Menü Elemente durch Drag und Drop '.
                                  'um Ihre Reihenfolge zu verändern.',
        'dynamic_route'        => 'Dynamische Route',
        'edit_item'            => 'Menü Element Editieren',
        'icon_class'           => 'Font Icon CSS-Klasse für das Menü Element (Benutze ',
        'icon_class2'          => 'Voyager Font CSS-Klasse</a>)',
        'icon_class_ph'        => 'Icon CSS-Klasse (optional)',
        'item_route'           => 'Route für das Menü Element',
        'item_title'           => 'Titel für das Menü Element',
        'link_type'            => 'Link Typ',
        'new_menu_item'        => 'Neues Menü Element',
        'open_in'              => 'Öffnen in',
        'open_new'             => 'Neuem Tab/Fenster',
        'open_same'            => 'Selber Tab/Fenster',
        'route_parameter'      => 'Route Parameter (falls vorhanden)',
        'static_url'           => 'Statische URL',
        'successfully_created' => 'Neues Menü Element erfolgreich erstellt.',
        'successfully_deleted' => 'Menü Element erfolgreich gelöscht.',
        'successfully_updated' => 'Menü Element erfolgreich aktualisiert.',
        'updated_order'        => 'Menü Reihenfolge erfolgreich aktualisiert.',
        'url'                  => 'URL des Menü Elements',
        'usage_hint'           => 'Sie können ein Menü überall auf der Seite ausgeben durch den Aufruf|'.
                                  'Sie können dieses Menü überall auf der Seite ausgeben durch den Aufruf',
    ],

    'post' => [
        'category'         => 'Post Kategorie',
        'content'          => 'Post Inhalt',
        'details'          => 'Post Details',
        'excerpt'          => 'Excerpt <small>Kurzbeschreibung dieses Posts</small>',
        'image'            => 'Post Bild',
        'meta_description' => 'Meta Beschreibung',
        'meta_keywords'    => 'Meta Keywords',
        'new'              => 'Post Anlegen',
        'seo_content'      => 'SEO Content',
        'seo_title'        => 'SEO Titel',
        'slug'             => 'URL Slug',
        'status'           => 'Post Status',
        'status_draft'     => 'Entwurf',
        'status_pending'   => 'Warten auf Freigabe',
        'status_published' => 'veröffentlicht',
        'title'            => 'Post Titel',
        'title_sub'        => 'Der Titel des Posts',
        'update'           => 'Post Aktualisieren',
    ],

    'database' => [
        'add_bread'                 => 'BREAD zu Tabelle Hinzufügen',
        'add_new_column'            => 'Neue Spalte Hinzufügen',
        'add_softdeletes'           => 'Soft Deletes Hinzufügen',
        'add_timestamps'            => 'Zeitstempel Hinzufügen',
        'already_exists'            => 'existiert bereits',
        'already_exists_table'      => 'Tabelle :table existiert bereits',
        'bread_crud_actions'        => 'BREAD/CRUD Aktionen',
        'bread_info'                => 'BREAD Info',
        'browse_bread'              => 'BREAD ansehen',
        'column'                    => 'Spalte',
        'composite_warning'         => 'Warnung: Diese Spalte ist Teil eines zusammengesetzten indexes',
        'controller_name'           => 'Controller Name',
        'controller_name_hint'      => 'z. B. PageController, falls leer gelassen wird der BREAD Controller verwendet',
        'create_bread_for_table'    => 'BREAD Erstellen für :table Tabelle',
        'create_migration'          => 'Migration Erstellen für diese Tabelle?',
        'create_model_table'        => 'Model für diese Tabelle erstellen?',
        'create_new_table'          => 'Neue Tabelle Erstellen',
        'create_your_new_table'     => 'Erstellen Sie Ihre neue Tabelle',
        'default'                   => 'Default',
        'delete_bread'              => 'BREAD Löschen',
        'delete_bread_before_table' => 'Sie müssen zuerst das BREAD von dieser Tabelle Entfernen '.
                                       'bevor Sie die Tabelle Löschen können.',
        'delete_table_bread_conf'  => 'Ja, BREAD Entfernen',
        'delete_table_bread_quest' => 'Sind Sie sicher, dass Sie das BREAD für Tabelle :table Löschen möchten?',
        'delete_table_confirm'     => 'Ja, diese Tabelle Löschen',
        'delete_table_question'    => 'Sind Sie sicher, dass Sie die Tabelle :table Löschen möchten?',
        'description'              => 'Beschreibung',
        'display_name'             => 'Anzeigename',
        'display_name_plural'      => 'Anzeigename (Plural)',
        'display_name_singular'    => 'Anzeigename (Singular)',
        'edit_bread'               => 'BREAD Bearbeiten',
        'edit_bread_for_table'     => 'Bearbeite BREAD für Tabelle :table',
        'edit_rows'                => 'Bearbeite die Zeilen für untenstehende Tabelle :table',
        'edit_table'               => 'Bearbeite die untenstehende Tabelle :table',
        'edit_table_not_exist'     => 'Die Tabelle welche Sie Bearbeiten möchten existiert nicht',
        'error_creating_bread'     => 'Es ist ein Fehler aufgetreten beim Versuch dieses BREAD anzulegen',
        'error_removing_bread'     => 'Es ist ein Fehler aufgetreten beim Versuch dieses BREAD zu Löschen',
        'error_updating_bread'     => 'Es ist ein Fehler aufgetreten beim Versuch dieses BREAD zu Aktualisieren',
        'extra'                    => 'Extra',
        'field'                    => 'Feld',
        'field_safe_failed'        => 'Konnte Feld :field nicht speichern, Änderungen zurückgerollt!',
        'generate_permissions'     => 'Zugriffsrechte Generieren',
        'icon_class'               => 'Icon CSS-Klasse für diese Tabelle',
        'icon_hint'                => 'Icon (optional) Benutze',
        'icon_hint2'               => 'Voyager Font CSS-Klasse',
        'index'                    => 'INDEX',
        'input_type'               => 'Input Typ',
        'key'                      => 'Key',
        'model_class'              => 'Name der Model Klasse',
        'model_name'               => 'Model Name',
        'model_name_ph'            => 'z. B. \App\Models\User, falls leer gelassen wird versucht den Namen der Tabelle '.
                                       'zu verwenden',
        'name_warning' => 'Sie müssen einen Namen für die Spalte vergeben, '.
                                       ' bevor Sie einen Index hinzufügen',
        'no_composites_warning' => 'Hinweis: Diese Tabelle hat zusammengesetzte Indexe. '.
                                       'Diese werden momentan nicht unterstützt. '.
                                       'Seien Sie vorsichtig beim Hinzufügen/Ändern von Indexen.',
        'null'             => 'Null',
        'optional_details' => 'Optionale Details',
        'policy_class'     => 'Policy Klassenname',
        'policy_name'      => 'Policy Name',
        'policy_name_ph'   => 'Bspw. \App\Policies\UserPolicy, falls leer gelassen wird versucht '.
                                      'den Default Wert zu Verwenden.',
        'primary'               => 'PRIMARY',
        'server_pagination'     => 'Serverseitige Pagination',
        'success_create_table'  => 'Tabelle :table erfolgreich erstellt',
        'success_created_bread' => 'Neues BREAD erfolgreich erstellt',
        'success_delete_table'  => 'Tabelle :table erfolgreich erstellt',
        'success_remove_bread'  => 'BREAD erfolgreich von :datatype entfernt',
        'success_update_bread'  => ':datatype BREAD erfolgreich aktualisiert',
        'success_update_table'  => 'Tabelle :table erfolgreich aktualisiert',
        'table_actions'         => 'Tabellen Aktionen',
        'table_columns'         => 'Tabellen Spalten',
        'table_has_index'       => 'Die Tabelle hat bereits einen primären Index.',
        'table_name'            => 'Tabellenname',
        'table_no_columns'      => 'Die Tabelle hat keine Spalten...',
        'type'                  => 'Typ',
        'type_not_supported'    => 'Dieser Typ wird nicht unterstützt',
        'unique'                => 'UNIQUE',
        'unknown_type'          => 'Unbekannter Typ',
        'update_table'          => 'Table Aktualisieren',
        'url_slug'              => 'URL Slug (muss unique sein)',
        'url_slug_ph'           => 'URL slug (z. B. posts)',
        'visibility'            => 'Sichtbarkeit',
    ],

    'dimmer' => [
        'page'           => 'Seite|Seiten',
        'page_link_text' => 'Alle Seiten Anzeigen',
        'page_text'      => 'Sie haben:count :string in Ihrer Datenbank. Klicken Sie auf untenstehenden Button '.
                            'um alle Seiten zu sehen.',
        'post'           => 'Post|Posts',
        'post_link_text' => 'Alle Posts Anzeigen',
        'post_text'      => 'Sie haben :count :string in Ihrer Datenbank. Klicken Sie auf untenstehenden Button ',
                            'um alle Posts zu sehen.',
        'user'           => 'Benutzer|Benutzer',
        'user_link_text' => 'Alle Benutzer Anzeigen',
        'user_text'      => 'Sie haben :count :string in Ihrer Datenbank. Klicken Sie auf untenstehenden Button ',
                            'um alle Benutzer zu sehen.',
    ],

    'form' => [
        'field_password_keep'          => 'Leer lassen um das aktuelle zu Behalten',
        'field_select_dd_relationship' => 'Stellen Sie sicher, dass Sie die entsprechende Relation in der '.
                                          ':method Methode der :class Klasse setzen.',
        'type_checkbox'       => 'Check Box',
        'type_codeeditor'     => 'Code Editor',
        'type_file'           => 'Datei',
        'type_image'          => 'Bild',
        'type_radiobutton'    => 'Radio Button',
        'type_richtextbox'    => 'Rich Textbox',
        'type_selectdropdown' => 'Select Dropdown',
        'type_textarea'       => 'Text Area',
        'type_textbox'        => 'Text Box',
    ],

    // DataTable translations from: https://github.com/DataTables/Plugins/tree/master/i18n
    'datatable' => [
        'sEmptyTable'     => 'Keine Daten vorhanden in dieser Tabelle',
        'sInfo'           => 'Zeige _START_ bis _END_ von _TOTAL_ Einträgen',
        'sInfoEmpty'      => 'Zeige 0 von 0 Einträgen',
        'sInfoFiltered'   => '(gefiltert von _MAX_ Einträgen insgesamt)',
        'sInfoPostFix'    => '',
        'sInfoThousands'  => '.',
        'sLengthMenu'     => 'Zeige _MENU_ Einträge',
        'sLoadingRecords' => 'Laden...',
        'sProcessing'     => 'Verarbeiten...',
        'sSearch'         => 'Suche:',
        'sZeroRecords'    => 'Keine passenden Einträge gefunden',
        'oPaginate'       => [
            'sFirst'    => 'Erste',
            'sLast'     => 'Letzte',
            'sNext'     => 'Nächste',
            'sPrevious' => 'Vorige',
        ],
        'oAria' => [
            'sSortAscending'  => ': Aktivieren um Spalte aufsteigend zu sortieren',
            'sSortDescending' => ': Aktivieren um Spalte absteigend zu sortieren',
        ],
    ],

    'theme' => [
        'footer_copyright'  => 'Made with <i class="voyager-heart"></i> by',
        'footer_copyright2' => 'Made with Rum und noch mehr Rum',
    ],

    'json' => [
        'invalid'           => 'Ungültiges JSON',
        'invalid_message'   => 'Es scheint Sie haben ungültiges JSON eingebracht.',
        'valid'             => 'Gültiges JSON',
        'validation_errors' => 'Validierungsfehler',
    ],

    'analytics' => [
        'by_pageview'  => 'nach pageview',
        'by_sessions'  => 'nach sessions',
        'by_users'     => 'nach users',
        'no_client_id' => 'Um Analytics zu sehen müssen Sie im Besitz einer google Analytics client id '.
                                     'sein und diese zu Ihren Settings hinzufügen für den Key '.
                                     '<code>google_analytics_client_id</code>. '.
                                     'Holen Sie sich Ihren Key in Ihrer Google developer console:',
        'set_view'               => 'eine Ansicht wählen',
        'this_vs_last_week'      => 'Diese Woche im Vergleich zu letzter Woche',
        'this_vs_last_year'      => 'Dieses Jahr im Vergleich zum letzten Jahr',
        'top_browsers'           => 'Top Browsers',
        'top_countries'          => 'Top Länder',
        'various_visualizations' => 'verschiedenartige Visualisierungen',
    ],

    'error' => [
        'symlink_created_text'  => 'Wir haben soeben den fehlenden Symlink für Sie angelegt.',
        'symlink_created_title' => 'Fehlenden Storage Symlink angelegt',
        'symlink_failed_text'   => 'Fehlender Symlink für Ihre Applikation konnte nicht angelegt werden. '.
                                    'Es scheint so als würde Ihr Hosting Provider dies nicht anbieten.',
        'symlink_failed_title'   => 'Fehlender Storage Symlink konnte nicht angelegt werden',
        'symlink_missing_button' => 'Bereinigen',
        'symlink_missing_text'   => 'Wir konnten keinen Storage Symlink finden. Dies könnte zu Problemen führen '.
                                    'beim Laden von Medien Dateien aus dem Browser.',
        'symlink_missing_title' => 'Fehlender Storage Symlink',
    ],
];
