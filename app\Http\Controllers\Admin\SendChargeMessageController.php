<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\ChargeConfig;
use App\Models\ChargeRequest;
use App\Models\Person;
use App\Models\Sale;
use Carbon\Carbon;

class SendChargeMessageController extends Controller
{
    private Person $person;
    private int $month;
    private int $year;

    public function __construct()
    {
        $this->middleware('auth');
    }

    public function send(Request $request, int $personId, int $month, int $year)
    {
        $this->month = $month;
        $this->year = $year;

        $this->setPerson($personId);
        $saleResume = $this->getSaleResume();
        $chargeConfig = $this->getChargeConfig();

        $message = $this->buildMessage([
            'person_name' => $this->person->name,
            'quantity' => $saleResume->quantity,
            'total' => $saleResume->formatted_total,
            'pix_name' => $chargeConfig->pix_name,
            'pix_key' =>   $chargeConfig->pix_key,
            'pix_type' => $chargeConfig->pix_type,
            'pix_bank' => $chargeConfig->pix_bank,
            'month' => $this->month,
            'year' => $this->year,
            'whatsapp_template' => $chargeConfig->whatsapp_template
        ]);

        $this->storeChargeRequest($personId, $saleResume, $message);

        $whatsappLink = self::generateWhatsappLink($this->person->phoneWithDDI, $message);

        return redirect($whatsappLink);
    }

    private function setPerson(int $personId): void
    {
        $this->person = Person::findOrFail($personId);
    }

    private function getSaleResume(): object
    {
        $resume = Sale::select(
            'person_id',
            DB::raw('SUM(quantity) as quantity'),
            DB::raw('SUM(total) as total')
        )
            ->where('person_id', $this->person->id)
            ->where('paid', false)
            ->whereMonth('sale_date', $this->month)
            ->whereYear('sale_date', $this->year)
            ->groupBy('person_id')
            ->firstOrFail();

        $resume->formatted_total = number_format($resume->total, 2, ',', '.');

        return $resume;
    }

    private function getChargeConfig(): object
    {
        return ChargeConfig::findOrFail(1);
    }

    private function buildMessage(array $data): string
    {
        $template = $data['whatsapp_template'];

        $template = $this->formatForWhatsapp($template);

        if ($data['month']) {
            $month = Carbon::create()->month((int)$data['month'])->translatedFormat('F');
            $template = str_replace('{{mes}}', $month, $template);
        }

        return str_replace(
            ['{{nome_cliente}}', '{{quantidade}}', '{{total}}', '{{pix_nome}}', '{{pix_chave}}', '{{pix_tipo}}', '{{pix_banco}}', '{{ano}}'],
            [$data['person_name'], $data['quantity'], $data['total'], $data['pix_name'], $data['pix_key'], $data['pix_type'], $data['pix_bank'], $data['year']],
            $template
        );
    }

    /**
     * Transforma uma mensagem HTML em uma mensagem formatada para ser enviada via WhatsApp.
     * Substitui as tags HTML por caracteres especiais do WhatsApp.
     * @param string $mensagem
     * @return string
     */
    private function formatForWhatsapp(string $mensagem): string
    {
        $message = preg_replace('/<br\s*\/?>/i', "\n", $mensagem);
        $mensagem = preg_replace('/<\/p\s*>/i', "\n", $mensagem);
        $mensagem = preg_replace('/<p[^>]*>/i', '', $mensagem);
        $message = str_replace(
            ['<b>', '</b>', '<strong>', '</strong>', '<i>', '</i>', '<em>', '</em>', '<s>', '</s>', '<code>'],
            ['*', '*', '*', '*', '_', '_', '_', '_', '~', '~', '`'],
            $mensagem
        );

        return trim(strip_tags(html_entity_decode($message)));
    }

    private function storeChargeRequest(int $personId, object $saleResume, string $message): void
    {
        ChargeRequest::create([
            'person_id' => $personId,
            'month' => $this->month,
            'year' => $this->year,
            'message' => $message,
            'quantity' => $saleResume->quantity,
            'total' => $saleResume->total,
        ]);
    }

    public static function generateWhatsappLink(string $phone, string $message): string
    {
        $encodedMessage = rawurlencode($message);
        return "https://api.whatsapp.com/send/?phone={$phone}&text={$encodedMessage}";
    }
}
